{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nfunction SidebarComponent_li_9_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isActiveRoute(item_r1.route));\n    i0.ɵɵproperty(\"routerLink\", item_r1.route);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(item_r1.icon + \" me-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r1.label, \" \");\n  }\n}\nfunction SidebarComponent_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, SidebarComponent_li_9_a_1_Template, 3, 6, \"a\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasAccess(item_r1.roles));\n  }\n}\nfunction SidebarComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"i\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 18);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.first_name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 2, ctx_r1.currentUser.role));\n  }\n}\nexport class SidebarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.menuItems = [{\n      label: 'Dashboard',\n      icon: 'fas fa-tachometer-alt',\n      route: '/dashboard',\n      roles: ['admin', 'manager', 'employee']\n    }, {\n      label: 'Products',\n      icon: 'fas fa-box',\n      route: '/products',\n      roles: ['admin', 'manager', 'employee']\n    }, {\n      label: 'Inventory',\n      icon: 'fas fa-warehouse',\n      route: '/inventory',\n      roles: ['admin', 'manager', 'employee']\n    }, {\n      label: 'Customers',\n      icon: 'fas fa-users',\n      route: '/customers',\n      roles: ['admin', 'manager', 'employee']\n    }, {\n      label: 'Orders',\n      icon: 'fas fa-shopping-cart',\n      route: '/orders',\n      roles: ['admin', 'manager', 'employee']\n    }, {\n      label: 'Analytics',\n      icon: 'fas fa-chart-bar',\n      route: '/analytics',\n      roles: ['admin', 'manager']\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  isActiveRoute(route) {\n    return this.router.url.startsWith(route);\n  }\n  hasAccess(roles) {\n    return this.authService.hasRole(roles);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      decls: 11,\n      vars: 2,\n      consts: [[1, \"sidebar\", \"d-flex\", \"flex-column\"], [1, \"sidebar-header\", \"p-4\", \"text-center\", \"border-bottom\", \"border-light\"], [1, \"text-white\", \"mb-0\"], [1, \"fas\", \"fa-store\", \"me-2\"], [1, \"text-light\", \"opacity-75\"], [1, \"sidebar-menu\", \"flex-grow-1\", \"p-3\"], [1, \"nav\", \"flex-column\"], [\"class\", \"nav-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"sidebar-footer p-3 border-top border-light\", 4, \"ngIf\"], [1, \"nav-item\"], [\"class\", \"nav-link d-flex align-items-center\", 3, \"routerLink\", \"active\", 4, \"ngIf\"], [1, \"nav-link\", \"d-flex\", \"align-items-center\", 3, \"routerLink\"], [1, \"sidebar-footer\", \"p-3\", \"border-top\", \"border-light\"], [1, \"d-flex\", \"align-items-center\", \"text-white\"], [1, \"avatar\", \"bg-white\", \"bg-opacity-25\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-3\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"fas\", \"fa-user\"], [1, \"flex-grow-1\"], [1, \"fw-bold\"], [1, \"opacity-75\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" EIM System \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"small\", 4);\n          i0.ɵɵtext(6, \"Inventory Management\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"ul\", 6);\n          i0.ɵɵtemplate(9, SidebarComponent_li_9_Template, 2, 1, \"li\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, SidebarComponent_div_10_Template, 10, 4, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      styles: [\".sidebar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n  color: white;\\n  width: 250px;\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8);\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  margin: 4px 0;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover, .nav-link.active[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: rgba(255, 255, 255, 0.15);\\n  transform: translateX(5px);\\n}\\n\\n.nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 20px;\\n  text-align: center;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r1", "isActiveRoute", "item_r1", "route", "ɵɵproperty", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate1", "label", "ɵɵtemplate", "SidebarComponent_li_9_a_1_Template", "hasAccess", "roles", "ɵɵtextInterpolate", "currentUser", "first_name", "ɵɵpipeBind1", "role", "SidebarComponent", "constructor", "authService", "router", "menuItems", "ngOnInit", "currentUser$", "subscribe", "user", "url", "startsWith", "hasRole", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_li_9_Template", "SidebarComponent_div_10_Template"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\components\\layout\\sidebar\\sidebar.component.ts", "D:\\Ecommerce Inventory Management\\frontend\\src\\app\\components\\layout\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../services/auth.service';\nimport { User } from '../../../models/user.model';\n\n@Component({\n  selector: 'app-sidebar',\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  currentUser: User | null = null;\n\n  menuItems = [\n    {\n      label: 'Dashboard',\n      icon: 'fas fa-tachometer-alt',\n      route: '/dashboard',\n      roles: ['admin', 'manager', 'employee']\n    },\n    {\n      label: 'Products',\n      icon: 'fas fa-box',\n      route: '/products',\n      roles: ['admin', 'manager', 'employee']\n    },\n    {\n      label: 'Inventory',\n      icon: 'fas fa-warehouse',\n      route: '/inventory',\n      roles: ['admin', 'manager', 'employee']\n    },\n    {\n      label: 'Customers',\n      icon: 'fas fa-users',\n      route: '/customers',\n      roles: ['admin', 'manager', 'employee']\n    },\n    {\n      label: 'Orders',\n      icon: 'fas fa-shopping-cart',\n      route: '/orders',\n      roles: ['admin', 'manager', 'employee']\n    },\n    {\n      label: 'Analytics',\n      icon: 'fas fa-chart-bar',\n      route: '/analytics',\n      roles: ['admin', 'manager']\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  isActiveRoute(route: string): boolean {\n    return this.router.url.startsWith(route);\n  }\n\n  hasAccess(roles: string[]): boolean {\n    return this.authService.hasRole(roles);\n  }\n}\n", "<nav class=\"sidebar d-flex flex-column\">\n  <div class=\"sidebar-header p-4 text-center border-bottom border-light\">\n    <h5 class=\"text-white mb-0\">\n      <i class=\"fas fa-store me-2\"></i>\n      EIM System\n    </h5>\n    <small class=\"text-light opacity-75\">Inventory Management</small>\n  </div>\n\n  <div class=\"sidebar-menu flex-grow-1 p-3\">\n    <ul class=\"nav flex-column\">\n      <li class=\"nav-item\" *ngFor=\"let item of menuItems\">\n        <a *ngIf=\"hasAccess(item.roles)\"\n           class=\"nav-link d-flex align-items-center\"\n           [routerLink]=\"item.route\"\n           [class.active]=\"isActiveRoute(item.route)\">\n          <i [class]=\"item.icon + ' me-3'\"></i>\n          {{ item.label }}\n        </a>\n      </li>\n    </ul>\n  </div>\n\n  <div class=\"sidebar-footer p-3 border-top border-light\" *ngIf=\"currentUser\">\n    <div class=\"d-flex align-items-center text-white\">\n      <div class=\"avatar bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3\" \n           style=\"width: 40px; height: 40px;\">\n        <i class=\"fas fa-user\"></i>\n      </div>\n      <div class=\"flex-grow-1\">\n        <div class=\"fw-bold\">{{ currentUser.first_name }}</div>\n        <small class=\"opacity-75\">{{ currentUser.role | titlecase }}</small>\n      </div>\n    </div>\n  </div>\n</nav>\n"], "mappings": ";;;;;ICYQA,EAAA,CAAAC,cAAA,YAG8C;IAC5CD,EAAA,CAAAE,SAAA,QAAqC;IACrCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAHDJ,EAAA,CAAAK,WAAA,WAAAC,MAAA,CAAAC,aAAA,CAAAC,OAAA,CAAAC,KAAA,EAA0C;IAD1CT,EAAA,CAAAU,UAAA,eAAAF,OAAA,CAAAC,KAAA,CAAyB;IAEvBT,EAAA,CAAAW,SAAA,EAA6B;IAA7BX,EAAA,CAAAY,UAAA,CAAAJ,OAAA,CAAAK,IAAA,WAA6B;IAChCb,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAN,OAAA,CAAAO,KAAA,MACF;;;;;IAPFf,EAAA,CAAAC,cAAA,YAAoD;IAClDD,EAAA,CAAAgB,UAAA,IAAAC,kCAAA,gBAG8C;IAIhDjB,EAAA,CAAAI,YAAA,EAAK;;;;;IAPCJ,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAY,SAAA,CAAAV,OAAA,CAAAW,KAAA,EAA2B;;;;;IAajCnB,EAFJ,CAAAC,cAAA,cAA4E,cACxB,cAER;IACtCD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAAyB,cACF;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACvDJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAkC;;IAGlEH,EAHkE,CAAAI,YAAA,EAAQ,EAChE,EACF,EACF;;;;IAJqBJ,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAoB,iBAAA,CAAAd,MAAA,CAAAe,WAAA,CAAAC,UAAA,CAA4B;IACvBtB,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAuB,WAAA,OAAAjB,MAAA,CAAAe,WAAA,CAAAG,IAAA,EAAkC;;;ADrBpE,OAAM,MAAOC,gBAAgB;EA0C3BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IA3ChB,KAAAP,WAAW,GAAgB,IAAI;IAE/B,KAAAQ,SAAS,GAAG,CACV;MACEd,KAAK,EAAE,WAAW;MAClBF,IAAI,EAAE,uBAAuB;MAC7BJ,KAAK,EAAE,YAAY;MACnBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU;KACvC,EACD;MACEJ,KAAK,EAAE,UAAU;MACjBF,IAAI,EAAE,YAAY;MAClBJ,KAAK,EAAE,WAAW;MAClBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU;KACvC,EACD;MACEJ,KAAK,EAAE,WAAW;MAClBF,IAAI,EAAE,kBAAkB;MACxBJ,KAAK,EAAE,YAAY;MACnBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU;KACvC,EACD;MACEJ,KAAK,EAAE,WAAW;MAClBF,IAAI,EAAE,cAAc;MACpBJ,KAAK,EAAE,YAAY;MACnBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU;KACvC,EACD;MACEJ,KAAK,EAAE,QAAQ;MACfF,IAAI,EAAE,sBAAsB;MAC5BJ,KAAK,EAAE,SAAS;MAChBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU;KACvC,EACD;MACEJ,KAAK,EAAE,WAAW;MAClBF,IAAI,EAAE,kBAAkB;MACxBJ,KAAK,EAAE,YAAY;MACnBU,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS;KAC3B,CACF;EAKE;EAEHW,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACZ,WAAW,GAAGY,IAAI;IACzB,CAAC,CAAC;EACJ;EAEA1B,aAAaA,CAACE,KAAa;IACzB,OAAO,IAAI,CAACmB,MAAM,CAACM,GAAG,CAACC,UAAU,CAAC1B,KAAK,CAAC;EAC1C;EAEAS,SAASA,CAACC,KAAe;IACvB,OAAO,IAAI,CAACQ,WAAW,CAACS,OAAO,CAACjB,KAAK,CAAC;EACxC;;;uBA3DWM,gBAAgB,EAAAzB,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBhB,gBAAgB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRzBhD,EAFJ,CAAAC,cAAA,aAAwC,aACiC,YACzC;UAC1BD,EAAA,CAAAE,SAAA,WAAiC;UACjCF,EAAA,CAAAG,MAAA,mBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAAqC;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UAC3DH,EAD2D,CAAAI,YAAA,EAAQ,EAC7D;UAGJJ,EADF,CAAAC,cAAA,aAA0C,YACZ;UAC1BD,EAAA,CAAAgB,UAAA,IAAAkC,8BAAA,gBAAoD;UAUxDlD,EADE,CAAAI,YAAA,EAAK,EACD;UAENJ,EAAA,CAAAgB,UAAA,KAAAmC,gCAAA,kBAA4E;UAY9EnD,EAAA,CAAAI,YAAA,EAAM;;;UAxBsCJ,EAAA,CAAAW,SAAA,GAAY;UAAZX,EAAA,CAAAU,UAAA,YAAAuC,GAAA,CAAApB,SAAA,CAAY;UAYG7B,EAAA,CAAAW,SAAA,EAAiB;UAAjBX,EAAA,CAAAU,UAAA,SAAAuC,GAAA,CAAA5B,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}