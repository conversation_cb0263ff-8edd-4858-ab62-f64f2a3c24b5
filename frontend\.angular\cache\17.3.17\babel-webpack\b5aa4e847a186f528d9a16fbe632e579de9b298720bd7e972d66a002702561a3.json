{"ast": null, "code": "import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nexport class Observable {\n  constructor(subscribe) {\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n  lift(operator) {\n    const observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n  subscribe(observerOrNext, error, complete) {\n    const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n    errorContext(() => {\n      const {\n        operator,\n        source\n      } = this;\n      subscriber.add(operator ? operator.call(subscriber, source) : source ? this._subscribe(subscriber) : this._trySubscribe(subscriber));\n    });\n    return subscriber;\n  }\n  _trySubscribe(sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      sink.error(err);\n    }\n  }\n  forEach(next, promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      const subscriber = new SafeSubscriber({\n        next: value => {\n          try {\n            next(value);\n          } catch (err) {\n            reject(err);\n            subscriber.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n      this.subscribe(subscriber);\n    });\n  }\n  _subscribe(subscriber) {\n    var _a;\n    return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n  }\n  [Symbol_observable]() {\n    return this;\n  }\n  pipe(...operations) {\n    return pipeFromArray(operations)(this);\n  }\n  toPromise(promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let value;\n      this.subscribe(x => value = x, err => reject(err), () => resolve(value));\n    });\n  }\n}\nObservable.create = subscribe => {\n  return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n  var _a;\n  return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n  return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n  return value && value instanceof Subscriber || isObserver(value) && isSubscription(value);\n}", "map": {"version": 3, "names": ["SafeSubscriber", "Subscriber", "isSubscription", "observable", "Symbol_observable", "pipeFromArray", "config", "isFunction", "errorContext", "Observable", "constructor", "subscribe", "_subscribe", "lift", "operator", "source", "observerOrNext", "error", "complete", "subscriber", "isSubscriber", "add", "call", "_trySubscribe", "sink", "err", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "value", "unsubscribe", "_a", "pipe", "operations", "to<PERSON>romise", "x", "create", "Promise", "isObserver"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/Observable.js"], "sourcesContent": ["import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nexport class Observable {\n    constructor(subscribe) {\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    lift(operator) {\n        const observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    }\n    subscribe(observerOrNext, error, complete) {\n        const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n        errorContext(() => {\n            const { operator, source } = this;\n            subscriber.add(operator\n                ?\n                    operator.call(subscriber, source)\n                : source\n                    ?\n                        this._subscribe(subscriber)\n                    :\n                        this._trySubscribe(subscriber));\n        });\n        return subscriber;\n    }\n    _trySubscribe(sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            sink.error(err);\n        }\n    }\n    forEach(next, promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            const subscriber = new SafeSubscriber({\n                next: (value) => {\n                    try {\n                        next(value);\n                    }\n                    catch (err) {\n                        reject(err);\n                        subscriber.unsubscribe();\n                    }\n                },\n                error: reject,\n                complete: resolve,\n            });\n            this.subscribe(subscriber);\n        });\n    }\n    _subscribe(subscriber) {\n        var _a;\n        return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n    }\n    [Symbol_observable]() {\n        return this;\n    }\n    pipe(...operations) {\n        return pipeFromArray(operations)(this);\n    }\n    toPromise(promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let value;\n            this.subscribe((x) => (value = x), (err) => reject(err), () => resolve(value));\n        });\n    }\n}\nObservable.create = (subscribe) => {\n    return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n    var _a;\n    return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n    return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n    return (value && value instanceof Subscriber) || (isObserver(value) && isSubscription(value));\n}\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,UAAU,QAAQ,cAAc;AACzD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,qBAAqB;AACrE,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAIA,SAAS,EAAE;MACX,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC/B;EACJ;EACAE,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMX,UAAU,GAAG,IAAIM,UAAU,CAAC,CAAC;IACnCN,UAAU,CAACY,MAAM,GAAG,IAAI;IACxBZ,UAAU,CAACW,QAAQ,GAAGA,QAAQ;IAC9B,OAAOX,UAAU;EACrB;EACAQ,SAASA,CAACK,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACvC,MAAMC,UAAU,GAAGC,YAAY,CAACJ,cAAc,CAAC,GAAGA,cAAc,GAAG,IAAIhB,cAAc,CAACgB,cAAc,EAAEC,KAAK,EAAEC,QAAQ,CAAC;IACtHV,YAAY,CAAC,MAAM;MACf,MAAM;QAAEM,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI;MACjCI,UAAU,CAACE,GAAG,CAACP,QAAQ,GAEfA,QAAQ,CAACQ,IAAI,CAACH,UAAU,EAAEJ,MAAM,CAAC,GACnCA,MAAM,GAEA,IAAI,CAACH,UAAU,CAACO,UAAU,CAAC,GAE3B,IAAI,CAACI,aAAa,CAACJ,UAAU,CAAC,CAAC;IAC/C,CAAC,CAAC;IACF,OAAOA,UAAU;EACrB;EACAI,aAAaA,CAACC,IAAI,EAAE;IAChB,IAAI;MACA,OAAO,IAAI,CAACZ,UAAU,CAACY,IAAI,CAAC;IAChC,CAAC,CACD,OAAOC,GAAG,EAAE;MACRD,IAAI,CAACP,KAAK,CAACQ,GAAG,CAAC;IACnB;EACJ;EACAC,OAAOA,CAACC,IAAI,EAAEC,WAAW,EAAE;IACvBA,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,CAACE,OAAO,EAAEC,MAAM,KAAK;MACxC,MAAMZ,UAAU,GAAG,IAAInB,cAAc,CAAC;QAClC2B,IAAI,EAAGK,KAAK,IAAK;UACb,IAAI;YACAL,IAAI,CAACK,KAAK,CAAC;UACf,CAAC,CACD,OAAOP,GAAG,EAAE;YACRM,MAAM,CAACN,GAAG,CAAC;YACXN,UAAU,CAACc,WAAW,CAAC,CAAC;UAC5B;QACJ,CAAC;QACDhB,KAAK,EAAEc,MAAM;QACbb,QAAQ,EAAEY;MACd,CAAC,CAAC;MACF,IAAI,CAACnB,SAAS,CAACQ,UAAU,CAAC;IAC9B,CAAC,CAAC;EACN;EACAP,UAAUA,CAACO,UAAU,EAAE;IACnB,IAAIe,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACnB,MAAM,MAAM,IAAI,IAAImB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvB,SAAS,CAACQ,UAAU,CAAC;EAC3F;EACA,CAACf,iBAAiB,IAAI;IAClB,OAAO,IAAI;EACf;EACA+B,IAAIA,CAAC,GAAGC,UAAU,EAAE;IAChB,OAAO/B,aAAa,CAAC+B,UAAU,CAAC,CAAC,IAAI,CAAC;EAC1C;EACAC,SAASA,CAACT,WAAW,EAAE;IACnBA,WAAW,GAAGC,cAAc,CAACD,WAAW,CAAC;IACzC,OAAO,IAAIA,WAAW,CAAC,CAACE,OAAO,EAAEC,MAAM,KAAK;MACxC,IAAIC,KAAK;MACT,IAAI,CAACrB,SAAS,CAAE2B,CAAC,IAAMN,KAAK,GAAGM,CAAE,EAAGb,GAAG,IAAKM,MAAM,CAACN,GAAG,CAAC,EAAE,MAAMK,OAAO,CAACE,KAAK,CAAC,CAAC;IAClF,CAAC,CAAC;EACN;AACJ;AACAvB,UAAU,CAAC8B,MAAM,GAAI5B,SAAS,IAAK;EAC/B,OAAO,IAAIF,UAAU,CAACE,SAAS,CAAC;AACpC,CAAC;AACD,SAASkB,cAAcA,CAACD,WAAW,EAAE;EACjC,IAAIM,EAAE;EACN,OAAO,CAACA,EAAE,GAAGN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGtB,MAAM,CAACkC,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGM,OAAO;AACxI;AACA,SAASC,UAAUA,CAACT,KAAK,EAAE;EACvB,OAAOA,KAAK,IAAIzB,UAAU,CAACyB,KAAK,CAACL,IAAI,CAAC,IAAIpB,UAAU,CAACyB,KAAK,CAACf,KAAK,CAAC,IAAIV,UAAU,CAACyB,KAAK,CAACd,QAAQ,CAAC;AACnG;AACA,SAASE,YAAYA,CAACY,KAAK,EAAE;EACzB,OAAQA,KAAK,IAAIA,KAAK,YAAY/B,UAAU,IAAMwC,UAAU,CAACT,KAAK,CAAC,IAAI9B,cAAc,CAAC8B,KAAK,CAAE;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}