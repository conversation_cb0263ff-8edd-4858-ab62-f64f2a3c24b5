{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/auth/login/login.component';\nimport { RegisterComponent } from './components/auth/register/register.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ProductListComponent } from './components/products/product-list/product-list.component';\nimport { ProductFormComponent } from './components/products/product-form/product-form.component';\nimport { InventoryListComponent } from './components/inventory/inventory-list/inventory-list.component';\nimport { InventoryFormComponent } from './components/inventory/inventory-form/inventory-form.component';\nimport { CustomerListComponent } from './components/customers/customer-list/customer-list.component';\nimport { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';\nimport { OrderListComponent } from './components/orders/order-list/order-list.component';\nimport { OrderFormComponent } from './components/orders/order-form/order-form.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}, {\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'products',\n  component: ProductListComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'products/new',\n  component: ProductFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'products/edit/:id',\n  component: ProductFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'inventory',\n  component: InventoryListComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'inventory/edit/:productId',\n  component: InventoryFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'customers',\n  component: CustomerListComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'customers/new',\n  component: CustomerFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'customers/edit/:id',\n  component: CustomerFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'orders',\n  component: OrderListComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'orders/new',\n  component: OrderFormComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'analytics',\n  component: AnalyticsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/dashboard'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "RegisterComponent", "DashboardComponent", "ProductListComponent", "ProductFormComponent", "InventoryListComponent", "InventoryFormComponent", "CustomerListComponent", "CustomerFormComponent", "OrderListComponent", "OrderFormComponent", "AnalyticsComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\n\nimport { LoginComponent } from './components/auth/login/login.component';\nimport { RegisterComponent } from './components/auth/register/register.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ProductListComponent } from './components/products/product-list/product-list.component';\nimport { ProductFormComponent } from './components/products/product-form/product-form.component';\nimport { InventoryListComponent } from './components/inventory/inventory-list/inventory-list.component';\nimport { InventoryFormComponent } from './components/inventory/inventory-form/inventory-form.component';\nimport { CustomerListComponent } from './components/customers/customer-list/customer-list.component';\nimport { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';\nimport { OrderListComponent } from './components/orders/order-list/order-list.component';\nimport { OrderFormComponent } from './components/orders/order-form/order-form.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\n\nimport { AuthGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  { path: 'register', component: RegisterComponent },\n  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },\n  { path: 'products', component: ProductListComponent, canActivate: [AuthGuard] },\n  { path: 'products/new', component: ProductFormComponent, canActivate: [AuthGuard] },\n  { path: 'products/edit/:id', component: ProductFormComponent, canActivate: [AuthGuard] },\n  { path: 'inventory', component: InventoryListComponent, canActivate: [AuthGuard] },\n  { path: 'inventory/edit/:productId', component: InventoryFormComponent, canActivate: [AuthGuard] },\n  { path: 'customers', component: CustomerListComponent, canActivate: [AuthGuard] },\n  { path: 'customers/new', component: CustomerFormComponent, canActivate: [AuthGuard] },\n  { path: 'customers/edit/:id', component: CustomerFormComponent, canActivate: [AuthGuard] },\n  { path: 'orders', component: OrderListComponent, canActivate: [AuthGuard] },\n  { path: 'orders/new', component: OrderFormComponent, canActivate: [AuthGuard] },\n  { path: 'analytics', component: AnalyticsComponent, canActivate: [AuthGuard] },\n  { path: '**', redirectTo: '/dashboard' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AAEtD,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,sBAAsB,QAAQ,gEAAgE;AACvG,SAASC,sBAAsB,QAAQ,gEAAgE;AACvG,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,kBAAkB,QAAQ,4CAA4C;AAE/E,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,EACzD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEjB;AAAc,CAAE,EAC5C;EAAEc,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEhB;AAAiB,CAAE,EAClD;EAAEa,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEf,kBAAkB;EAAEgB,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC9E;EAAEE,IAAI,EAAE,UAAU;EAAEG,SAAS,EAAEd,oBAAoB;EAAEe,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC/E;EAAEE,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEb,oBAAoB;EAAEc,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACnF;EAAEE,IAAI,EAAE,mBAAmB;EAAEG,SAAS,EAAEb,oBAAoB;EAAEc,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACxF;EAAEE,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEZ,sBAAsB;EAAEa,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAClF;EAAEE,IAAI,EAAE,2BAA2B;EAAEG,SAAS,EAAEX,sBAAsB;EAAEY,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAClG;EAAEE,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEV,qBAAqB;EAAEW,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACjF;EAAEE,IAAI,EAAE,eAAe;EAAEG,SAAS,EAAET,qBAAqB;EAAEU,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACrF;EAAEE,IAAI,EAAE,oBAAoB;EAAEG,SAAS,EAAET,qBAAqB;EAAEU,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC1F;EAAEE,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAER,kBAAkB;EAAES,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC3E;EAAEE,IAAI,EAAE,YAAY;EAAEG,SAAS,EAAEP,kBAAkB;EAAEQ,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC/E;EAAEE,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEN,kBAAkB;EAAEO,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC9E;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAY,CAAE,CACzC;AAMD,OAAM,MAAOI,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBpB,YAAY,CAACqB,OAAO,CAACP,MAAM,CAAC,EAC5Bd,YAAY;IAAA;EAAA;;;2EAEXoB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFjBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}