.app-container {
  height: 100vh;
  overflow: hidden;
}

.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.main-layout {
  height: 100vh;
}

.sidebar-container {
  width: 250px;
  flex-shrink: 0;
}

.main-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  background-color: #f8f9fa;
}

@media (max-width: 768px) {
  .sidebar-container {
    position: fixed;
    top: 0;
    left: -250px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .sidebar-container.show {
    left: 0;
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}
