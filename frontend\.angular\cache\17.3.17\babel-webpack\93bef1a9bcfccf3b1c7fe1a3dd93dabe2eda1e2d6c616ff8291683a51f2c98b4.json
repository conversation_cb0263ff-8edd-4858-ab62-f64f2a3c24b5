{"ast": null, "code": "import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n  return new Observable(subscriber => subscribable.subscribe(subscriber));\n}", "map": {"version": 3, "names": ["Observable", "fromSubscribable", "subscribable", "subscriber", "subscribe"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/observable/fromSubscribable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n    return new Observable((subscriber) => subscribable.subscribe(subscriber));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EAC3C,OAAO,IAAIF,UAAU,CAAEG,UAAU,IAAKD,YAAY,CAACE,SAAS,CAACD,UAAU,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}