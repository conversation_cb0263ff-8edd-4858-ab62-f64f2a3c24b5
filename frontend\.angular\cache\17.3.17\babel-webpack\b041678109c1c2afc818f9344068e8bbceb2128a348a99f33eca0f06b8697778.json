{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n  return skipCount <= 0 ? identity : operate((source, subscriber) => {\n    let ring = new Array(skipCount);\n    let seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const valueIndex = seen++;\n      if (valueIndex < skipCount) {\n        ring[valueIndex] = value;\n      } else {\n        const index = valueIndex % skipCount;\n        const oldValue = ring[index];\n        ring[index] = value;\n        subscriber.next(oldValue);\n      }\n    }));\n    return () => {\n      ring = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "skipLast", "skip<PERSON><PERSON>nt", "source", "subscriber", "ring", "Array", "seen", "subscribe", "value", "valueIndex", "index", "oldValue", "next"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/operators/skipLast.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n    return skipCount <= 0\n        ?\n            identity\n        : operate((source, subscriber) => {\n            let ring = new Array(skipCount);\n            let seen = 0;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                const valueIndex = seen++;\n                if (valueIndex < skipCount) {\n                    ring[valueIndex] = value;\n                }\n                else {\n                    const index = valueIndex % skipCount;\n                    const oldValue = ring[index];\n                    ring[index] = value;\n                    subscriber.next(oldValue);\n                }\n            }));\n            return () => {\n                ring = null;\n            };\n        });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,QAAQA,CAACC,SAAS,EAAE;EAChC,OAAOA,SAAS,IAAI,CAAC,GAEbJ,QAAQ,GACVC,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,IAAI,GAAG,IAAIC,KAAK,CAACJ,SAAS,CAAC;IAC/B,IAAIK,IAAI,GAAG,CAAC;IACZJ,MAAM,CAACK,SAAS,CAACR,wBAAwB,CAACI,UAAU,EAAGK,KAAK,IAAK;MAC7D,MAAMC,UAAU,GAAGH,IAAI,EAAE;MACzB,IAAIG,UAAU,GAAGR,SAAS,EAAE;QACxBG,IAAI,CAACK,UAAU,CAAC,GAAGD,KAAK;MAC5B,CAAC,MACI;QACD,MAAME,KAAK,GAAGD,UAAU,GAAGR,SAAS;QACpC,MAAMU,QAAQ,GAAGP,IAAI,CAACM,KAAK,CAAC;QAC5BN,IAAI,CAACM,KAAK,CAAC,GAAGF,KAAK;QACnBL,UAAU,CAACS,IAAI,CAACD,QAAQ,CAAC;MAC7B;IACJ,CAAC,CAAC,CAAC;IACH,OAAO,MAAM;MACTP,IAAI,GAAG,IAAI;IACf,CAAC;EACL,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}