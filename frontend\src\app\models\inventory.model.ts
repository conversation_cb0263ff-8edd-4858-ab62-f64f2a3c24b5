export interface Inventory {
  id: number;
  product_id: number;
  product_name?: string;
  product_sku?: string;
  category_name?: string;
  quantity: number;
  min_stock_level: number;
  max_stock_level: number;
  location?: string;
  last_updated: Date;
}

export interface InventoryMovement {
  id: number;
  product_id: number;
  product_name?: string;
  product_sku?: string;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reference_type: 'purchase' | 'sale' | 'return' | 'adjustment';
  reference_id?: number;
  notes?: string;
  created_at: Date;
  created_by: number;
  first_name?: string;
  last_name?: string;
}

export interface InventoryUpdateRequest {
  quantity: number;
  min_stock_level: number;
  max_stock_level: number;
  location?: string;
}

export interface InventoryAdjustmentRequest {
  adjustment: number;
  notes?: string;
}
