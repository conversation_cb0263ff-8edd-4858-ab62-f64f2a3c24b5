{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = environment.apiUrl;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.checkAuthStatus();\n  }\n  checkAuthStatus() {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    if (token && user) {\n      try {\n        const userData = JSON.parse(user);\n        this.currentUserSubject.next(userData);\n        this.isAuthenticatedSubject.next(true);\n      } catch (error) {\n        this.logout();\n      }\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      if (response.token && response.user) {\n        localStorage.setItem('token', response.token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }));\n  }\n  register(userData) {\n    return this.http.post(`${this.apiUrl}/auth/register`, userData).pipe(tap(response => {\n      if (response.token && response.user) {\n        localStorage.setItem('token', response.token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }\n    }));\n  }\n  logout() {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  hasRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  getProfile() {\n    return this.http.get(`${this.apiUrl}/auth/profile`);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "AuthService", "constructor", "http", "router", "apiUrl", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "checkAuthStatus", "token", "localStorage", "getItem", "user", "userData", "JSON", "parse", "next", "error", "logout", "login", "credentials", "post", "pipe", "response", "setItem", "stringify", "register", "removeItem", "navigate", "getToken", "getCurrentUser", "value", "isAuthenticated", "hasRole", "roles", "includes", "role", "getProfile", "get", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { environment } from '../../environments/environment';\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private apiUrl = environment.apiUrl;\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    this.checkAuthStatus();\n  }\n\n  private checkAuthStatus(): void {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    \n    if (token && user) {\n      try {\n        const userData = JSON.parse(user);\n        this.currentUserSubject.next(userData);\n        this.isAuthenticatedSubject.next(true);\n      } catch (error) {\n        this.logout();\n      }\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          if (response.token && response.user) {\n            localStorage.setItem('token', response.token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            this.currentUserSubject.next(response.user);\n            this.isAuthenticatedSubject.next(true);\n          }\n        })\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          if (response.token && response.user) {\n            localStorage.setItem('token', response.token);\n            localStorage.setItem('user', JSON.stringify(response.user));\n            this.currentUserSubject.next(response.user);\n            this.isAuthenticatedSubject.next(true);\n          }\n        })\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/login']);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  hasRole(roles: string[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  getProfile(): Observable<{ user: User }> {\n    return this.http.get<{ user: User }>(`${this.apiUrl}/auth/profile`);\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AAGvD,SAASC,WAAW,QAAQ,gCAAgC;;;;AAM5D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATR,KAAAC,MAAM,GAAGL,WAAW,CAACK,MAAM;IAC3B,KAAAC,kBAAkB,GAAG,IAAIR,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAS,sBAAsB,GAAG,IAAIT,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAU,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;IAMlE,IAAI,CAACE,eAAe,EAAE;EACxB;EAEQA,eAAeA,CAAA;IACrB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAEzC,IAAIF,KAAK,IAAIG,IAAI,EAAE;MACjB,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;QACjC,IAAI,CAACT,kBAAkB,CAACa,IAAI,CAACH,QAAQ,CAAC;QACtC,IAAI,CAACT,sBAAsB,CAACY,IAAI,CAAC,IAAI,CAAC;OACvC,CAAC,OAAOC,KAAK,EAAE;QACd,IAAI,CAACC,MAAM,EAAE;;;EAGnB;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,MAAM,aAAa,EAAEkB,WAAW,CAAC,CAC1EE,IAAI,CACH1B,GAAG,CAAC2B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACd,KAAK,IAAIc,QAAQ,CAACX,IAAI,EAAE;QACnCF,YAAY,CAACc,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACd,KAAK,CAAC;QAC7CC,YAAY,CAACc,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACW,SAAS,CAACF,QAAQ,CAACX,IAAI,CAAC,CAAC;QAC3D,IAAI,CAACT,kBAAkB,CAACa,IAAI,CAACO,QAAQ,CAACX,IAAI,CAAC;QAC3C,IAAI,CAACR,sBAAsB,CAACY,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,CACH;EACL;EAEAU,QAAQA,CAACb,QAAyB;IAChC,OAAO,IAAI,CAACb,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,MAAM,gBAAgB,EAAEW,QAAQ,CAAC,CAC1ES,IAAI,CACH1B,GAAG,CAAC2B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACd,KAAK,IAAIc,QAAQ,CAACX,IAAI,EAAE;QACnCF,YAAY,CAACc,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACd,KAAK,CAAC;QAC7CC,YAAY,CAACc,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACW,SAAS,CAACF,QAAQ,CAACX,IAAI,CAAC,CAAC;QAC3D,IAAI,CAACT,kBAAkB,CAACa,IAAI,CAACO,QAAQ,CAACX,IAAI,CAAC;QAC3C,IAAI,CAACR,sBAAsB,CAACY,IAAI,CAAC,IAAI,CAAC;;IAE1C,CAAC,CAAC,CACH;EACL;EAEAE,MAAMA,CAAA;IACJR,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;IAChCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,CAACxB,kBAAkB,CAACa,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACZ,sBAAsB,CAACY,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACf,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,QAAQA,CAAA;IACN,OAAOnB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EACtC;EAEAmB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC3B,kBAAkB,CAAC4B,KAAK;EACtC;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC5B,sBAAsB,CAAC2B,KAAK;EAC1C;EAEAE,OAAOA,CAACC,KAAe;IACrB,MAAMtB,IAAI,GAAG,IAAI,CAACkB,cAAc,EAAE;IAClC,OAAOlB,IAAI,GAAGsB,KAAK,CAACC,QAAQ,CAACvB,IAAI,CAACwB,IAAI,CAAC,GAAG,KAAK;EACjD;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACrC,IAAI,CAACsC,GAAG,CAAiB,GAAG,IAAI,CAACpC,MAAM,eAAe,CAAC;EACrE;;;uBArFWJ,WAAW,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX9C,WAAW;MAAA+C,OAAA,EAAX/C,WAAW,CAAAgD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}