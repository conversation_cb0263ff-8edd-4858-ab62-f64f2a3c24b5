export interface Customer {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  total_orders?: number;
  total_spent?: number;
  last_order_date?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CustomerCreateRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
}
