{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      this.router.navigate(['/login']);\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "isAuthenticated", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): boolean {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      this.router.navigate(['/login']);\n      return false;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,WAAW,CAACG,eAAe,EAAE,EAAE;MACtC,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,OAAO,KAAK;;EAEhB;;;uBAdWN,SAAS,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATZ,SAAS;MAAAa,OAAA,EAATb,SAAS,CAAAc,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}