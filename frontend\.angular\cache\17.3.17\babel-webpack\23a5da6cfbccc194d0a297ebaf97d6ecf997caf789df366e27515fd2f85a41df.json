{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    const token = this.authService.getToken();\n    if (token) {\n      req = req.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n    return next.handle(req).pipe(catchError(error => {\n      if (error.status === 401 || error.status === 403) {\n        this.authService.logout();\n      }\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "AuthInterceptor", "constructor", "authService", "intercept", "req", "next", "token", "getToken", "clone", "setHeaders", "Authorization", "handle", "pipe", "error", "status", "logout", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {\n    const token = this.authService.getToken();\n    \n    if (token) {\n      req = req.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n    }\n\n    return next.handle(req).pipe(\n      catchError((error: HttpErrorResponse) => {\n        if (error.status === 401 || error.status === 403) {\n          this.authService.logout();\n        }\n        return throwError(() => error);\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAQ,gBAAgB;;;AAI3C,OAAM,MAAOC,eAAe;EAE1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,MAAMC,KAAK,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IAEzC,IAAID,KAAK,EAAE;MACTF,GAAG,GAAGA,GAAG,CAACI,KAAK,CAAC;QACdC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUJ,KAAK;;OAEjC,CAAC;;IAGJ,OAAOD,IAAI,CAACM,MAAM,CAACP,GAAG,CAAC,CAACQ,IAAI,CAC1Bb,UAAU,CAAEc,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QAChD,IAAI,CAACZ,WAAW,CAACa,MAAM,EAAE;;MAE3B,OAAOjB,UAAU,CAAC,MAAMe,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;;;uBAvBWb,eAAe,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAfnB,eAAe;MAAAoB,OAAA,EAAfpB,eAAe,CAAAqB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}