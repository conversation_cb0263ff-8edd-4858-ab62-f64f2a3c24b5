export interface Order {
  id: number;
  customer_id: number;
  order_number: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total_amount: number;
  order_date: Date;
  shipping_address?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  created_at: Date;
  updated_at: Date;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product_name?: string;
  sku?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface OrderCreateRequest {
  customer_id: number;
  items: OrderItemRequest[];
  shipping_address?: string;
}

export interface OrderItemRequest {
  product_id: number;
  quantity: number;
}

export interface OrderStatusUpdateRequest {
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
}
