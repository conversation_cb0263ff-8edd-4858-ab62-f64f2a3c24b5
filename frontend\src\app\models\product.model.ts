export interface Product {
  id: number;
  name: string;
  description: string;
  sku: string;
  category_id: number;
  category_name?: string;
  price: number;
  cost_price: number;
  image_url?: string;
  status: 'active' | 'inactive';
  stock_quantity?: number;
  created_at: Date;
  updated_at: Date;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
  parent_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface ProductCreateRequest {
  name: string;
  description: string;
  sku: string;
  category_id: number;
  price: number;
  cost_price: number;
  image_url?: string;
  status?: 'active' | 'inactive';
}
