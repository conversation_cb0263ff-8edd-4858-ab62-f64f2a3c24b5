export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  details?: any[];
}

export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface DashboardStats {
  overall_stats: {
    total_products: number;
    total_customers: number;
    total_orders: number;
    total_revenue: number;
    low_stock_items: number;
  };
  monthly_sales: {
    month: string;
    order_count: number;
    revenue: number;
  }[];
  top_products: {
    id: number;
    name: string;
    sku: string;
    total_sold: number;
    total_revenue: number;
  }[];
  recent_activities: {
    type: string;
    id: number;
    reference: string;
    amount: number;
    created_at: Date;
    customer_name: string;
  }[];
}
