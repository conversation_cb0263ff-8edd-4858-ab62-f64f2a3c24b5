{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Components\nimport { LoginComponent } from './components/auth/login/login.component';\nimport { RegisterComponent } from './components/auth/register/register.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ProductListComponent } from './components/products/product-list/product-list.component';\nimport { ProductFormComponent } from './components/products/product-form/product-form.component';\nimport { InventoryListComponent } from './components/inventory/inventory-list/inventory-list.component';\nimport { InventoryFormComponent } from './components/inventory/inventory-form/inventory-form.component';\nimport { CustomerListComponent } from './components/customers/customer-list/customer-list.component';\nimport { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';\nimport { OrderListComponent } from './components/orders/order-list/order-list.component';\nimport { OrderFormComponent } from './components/orders/order-form/order-form.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport { SidebarComponent } from './components/layout/sidebar/sidebar.component';\nimport { HeaderComponent } from './components/layout/header/header.component';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { ProductService } from './services/product.service';\nimport { InventoryService } from './services/inventory.service';\nimport { CustomerService } from './services/customer.service';\nimport { OrderService } from './services/order.service';\nimport { AnalyticsService } from './services/analytics.service';\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, LoginComponent, RegisterComponent, DashboardComponent, ProductListComponent, ProductFormComponent, InventoryListComponent, InventoryFormComponent, CustomerListComponent, CustomerFormComponent, OrderListComponent, OrderFormComponent, AnalyticsComponent, SidebarComponent, HeaderComponent],\n  imports: [BrowserModule, AppRoutingModule, HttpClientModule, ReactiveFormsModule, FormsModule, BrowserAnimationsModule],\n  providers: [AuthService, ProductService, InventoryService, CustomerService, OrderService, AnalyticsService, AuthGuard, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "HttpClientModule", "HTTP_INTERCEPTORS", "ReactiveFormsModule", "FormsModule", "BrowserAnimationsModule", "AppRoutingModule", "AppComponent", "LoginComponent", "RegisterComponent", "DashboardComponent", "ProductListComponent", "ProductFormComponent", "InventoryListComponent", "InventoryFormComponent", "CustomerListComponent", "CustomerFormComponent", "OrderListComponent", "OrderFormComponent", "AnalyticsComponent", "SidebarComponent", "HeaderComponent", "AuthService", "ProductService", "InventoryService", "CustomerService", "OrderService", "AnalyticsService", "<PERSON><PERSON><PERSON><PERSON>", "AuthInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Components\nimport { LoginComponent } from './components/auth/login/login.component';\nimport { RegisterComponent } from './components/auth/register/register.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { ProductListComponent } from './components/products/product-list/product-list.component';\nimport { ProductFormComponent } from './components/products/product-form/product-form.component';\nimport { InventoryListComponent } from './components/inventory/inventory-list/inventory-list.component';\nimport { InventoryFormComponent } from './components/inventory/inventory-form/inventory-form.component';\nimport { CustomerListComponent } from './components/customers/customer-list/customer-list.component';\nimport { CustomerFormComponent } from './components/customers/customer-form/customer-form.component';\nimport { OrderListComponent } from './components/orders/order-list/order-list.component';\nimport { OrderFormComponent } from './components/orders/order-form/order-form.component';\nimport { AnalyticsComponent } from './components/analytics/analytics.component';\nimport { SidebarComponent } from './components/layout/sidebar/sidebar.component';\nimport { HeaderComponent } from './components/layout/header/header.component';\n\n// Services\nimport { AuthService } from './services/auth.service';\nimport { ProductService } from './services/product.service';\nimport { InventoryService } from './services/inventory.service';\nimport { CustomerService } from './services/customer.service';\nimport { OrderService } from './services/order.service';\nimport { AnalyticsService } from './services/analytics.service';\n\n// Guards\nimport { AuthGuard } from './guards/auth.guard';\n\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    RegisterComponent,\n    DashboardComponent,\n    ProductListComponent,\n    ProductFormComponent,\n    InventoryListComponent,\n    InventoryFormComponent,\n    CustomerListComponent,\n    CustomerFormComponent,\n    OrderListComponent,\n    OrderFormComponent,\n    AnalyticsComponent,\n    SidebarComponent,\n    HeaderComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    HttpClientModule,\n    ReactiveFormsModule,\n    FormsModule,\n    BrowserAnimationsModule\n  ],\n  providers: [\n    AuthService,\n    ProductService,\n    InventoryService,\n    CustomerService,\n    OrderService,\n    AnalyticsService,\n    AuthGuard,\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,sBAAsB,QAAQ,gEAAgE;AACvG,SAASC,sBAAsB,QAAQ,gEAAgE;AACvG,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,qBAAqB,QAAQ,8DAA8D;AACpG,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,eAAe,QAAQ,6CAA6C;AAE7E;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,gBAAgB,QAAQ,8BAA8B;AAE/D;AACA,SAASC,SAAS,QAAQ,qBAAqB;AAE/C;AACA,SAASC,eAAe,QAAQ,iCAAiC;AA4C1D,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EA1CrBhC,QAAQ,CAAC;EACRiC,YAAY,EAAE,CACZzB,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,oBAAoB,EACpBC,sBAAsB,EACtBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB,EAClBC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAChBC,eAAe,CAChB;EACDY,OAAO,EAAE,CACPjC,aAAa,EACbM,gBAAgB,EAChBL,gBAAgB,EAChBE,mBAAmB,EACnBC,WAAW,EACXC,uBAAuB,CACxB;EACD6B,SAAS,EAAE,CACTZ,WAAW,EACXC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,EACfC,YAAY,EACZC,gBAAgB,EAChBC,SAAS,EACT;IACEO,OAAO,EAAEjC,iBAAiB;IAC1BkC,QAAQ,EAAEP,eAAe;IACzBQ,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAAC/B,YAAY;CACzB,CAAC,C,EACWuB,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}