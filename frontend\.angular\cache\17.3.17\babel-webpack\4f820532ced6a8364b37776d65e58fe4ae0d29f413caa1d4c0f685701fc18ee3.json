{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMap(project, resultSelector) {\n  return isFunction(resultSelector) ? mergeMap(project, resultSelector, 1) : mergeMap(project, 1);\n}", "map": {"version": 3, "names": ["mergeMap", "isFunction", "concatMap", "project", "resultSelector"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/operators/concatMap.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMap(project, resultSelector) {\n    return isFunction(resultSelector) ? mergeMap(project, resultSelector, 1) : mergeMap(project, 1);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,SAASA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAC/C,OAAOH,UAAU,CAACG,cAAc,CAAC,GAAGJ,QAAQ,CAACG,OAAO,EAAEC,cAAc,EAAE,CAAC,CAAC,GAAGJ,QAAQ,CAACG,OAAO,EAAE,CAAC,CAAC;AACnG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}