{"ast": null, "code": "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, ANIMATION_MODULE_TYPE, ViewEncapsulation, ɵRuntimeError, Inject } from '@angular/core';\n\n/**\n * @description Constants for the categories of parameters that can be defined for animations.\n *\n * A corresponding function defines a set of parameters for each category, and\n * collects them into a corresponding `AnimationMetadata` object.\n *\n * @publicApi\n */\nvar AnimationMetadataType;\n(function (AnimationMetadataType) {\n  /**\n   * Associates a named animation state with a set of CSS styles.\n   * See [`state()`](api/animations/state)\n   */\n  AnimationMetadataType[AnimationMetadataType[\"State\"] = 0] = \"State\";\n  /**\n   * Data for a transition from one animation state to another.\n   * See `transition()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Transition\"] = 1] = \"Transition\";\n  /**\n   * Contains a set of animation steps.\n   * See `sequence()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Sequence\"] = 2] = \"Sequence\";\n  /**\n   * Contains a set of animation steps.\n   * See `{@link animations/group group()}`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Group\"] = 3] = \"Group\";\n  /**\n   * Contains an animation step.\n   * See `animate()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Animate\"] = 4] = \"Animate\";\n  /**\n   * Contains a set of animation steps.\n   * See `keyframes()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Keyframes\"] = 5] = \"Keyframes\";\n  /**\n   * Contains a set of CSS property-value pairs into a named style.\n   * See `style()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Style\"] = 6] = \"Style\";\n  /**\n   * Associates an animation with an entry trigger that can be attached to an element.\n   * See `trigger()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Trigger\"] = 7] = \"Trigger\";\n  /**\n   * Contains a re-usable animation.\n   * See `animation()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Reference\"] = 8] = \"Reference\";\n  /**\n   * Contains data to use in executing child animations returned by a query.\n   * See `animateChild()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"AnimateChild\"] = 9] = \"AnimateChild\";\n  /**\n   * Contains animation parameters for a re-usable animation.\n   * See `useAnimation()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"AnimateRef\"] = 10] = \"AnimateRef\";\n  /**\n   * Contains child-animation query data.\n   * See `query()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Query\"] = 11] = \"Query\";\n  /**\n   * Contains data for staggering an animation sequence.\n   * See `stagger()`\n   */\n  AnimationMetadataType[AnimationMetadataType[\"Stagger\"] = 12] = \"Stagger\";\n})(AnimationMetadataType || (AnimationMetadataType = {}));\n/**\n * Specifies automatic styling.\n *\n * @publicApi\n */\nconst AUTO_STYLE = '*';\n/**\n * Creates a named animation trigger, containing a  list of [`state()`](api/animations/state)\n * and `transition()` entries to be evaluated when the expression\n * bound to the trigger changes.\n *\n * @param name An identifying string.\n * @param definitions  An animation definition object, containing an array of\n * [`state()`](api/animations/state) and `transition()` declarations.\n *\n * @return An object that encapsulates the trigger data.\n *\n * @usageNotes\n * Define an animation trigger in the `animations` section of `@Component` metadata.\n * In the template, reference the trigger by name and bind it to a trigger expression that\n * evaluates to a defined animation state, using the following format:\n *\n * `[@triggerName]=\"expression\"`\n *\n * Animation trigger bindings convert all values to strings, and then match the\n * previous and current values against any linked transitions.\n * Booleans can be specified as `1` or `true` and `0` or `false`.\n *\n * ### Usage Example\n *\n * The following example creates an animation trigger reference based on the provided\n * name value.\n * The provided animation value is expected to be an array consisting of state and\n * transition declarations.\n *\n * ```typescript\n * @Component({\n *   selector: \"my-component\",\n *   templateUrl: \"my-component-tpl.html\",\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component makes use of the defined trigger\n * by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ### Using an inline function\n * The `transition` animation method also supports reading an inline function which can decide\n * if its associated animation should be run.\n *\n * ```typescript\n * // this method is run each time the `myAnimationTrigger` trigger value changes.\n * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n string]: any}): boolean {\n *   // notice that `element` and `params` are also available here\n *   return toState == 'yes-please-animate';\n * }\n *\n * @Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger('myAnimationTrigger', [\n *       transition(myInlineMatcherFn, [\n *         // the animation sequence code\n *       ]),\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"yes-please-animate\";\n * }\n * ```\n *\n * ### Disabling Animations\n * When true, the special animation control binding `@.disabled` binding prevents\n * all animations from rendering.\n * Place the  `@.disabled` binding on an element to disable\n * animations on the element itself, as well as any inner animation triggers\n * within the element.\n *\n * The following example shows how to use this feature:\n *\n * ```typescript\n * @Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [@.disabled]=\"isDisabled\">\n *       <div [@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n * along with any inner animations.\n *\n * ### Disable animations application-wide\n * When an area of the template is set to have animations disabled,\n * **all** inner components have their animations disabled as well.\n * This means that you can disable all animations for an app\n * by placing a host binding set on `@.disabled` on the topmost Angular component.\n *\n * ```typescript\n * import {Component, HostBinding} from '@angular/core';\n *\n * @Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   @HostBinding('@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### Overriding disablement of inner animations\n * Despite inner animations being disabled, a parent animation can `query()`\n * for inner elements located in disabled areas of the template and still animate\n * them if needed. This is also the case for when a sub animation is\n * queried by a parent and then later animated using `animateChild()`.\n *\n * ### Detecting when an animation is disabled\n * If a region of the DOM (or the entire application) has its animations disabled, the animation\n * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n * an instance of an `AnimationEvent`. If animations are disabled,\n * the `.disabled` flag on the event is true.\n *\n * @publicApi\n */\nfunction trigger(name, definitions) {\n  return {\n    type: AnimationMetadataType.Trigger,\n    name,\n    definitions,\n    options: {}\n  };\n}\n/**\n * Defines an animation step that combines styling information with timing information.\n *\n * @param timings Sets `AnimateTimings` for the parent animation.\n * A string in the format \"duration [delay] [easing]\".\n *  - Duration and delay are expressed as a number and optional time unit,\n * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n * The default unit is milliseconds.\n *  - The easing value controls how the animation accelerates and decelerates\n * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n * `ease-in-out`, or a `cubic-bezier()` function call.\n * If not supplied, no easing is applied.\n *\n * For example, the string \"1s 100ms ease-out\" specifies a duration of\n * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n * which decelerates near the end of the duration.\n * @param styles Sets AnimationStyles for the parent animation.\n * A function call to either `style()` or `keyframes()`\n * that returns a collection of CSS style entries to be applied to the parent animation.\n * When null, uses the styles from the destination state.\n * This is useful when describing an animation step that will complete an animation;\n * see \"Animating to the final state\" in `transitions()`.\n * @returns An object that encapsulates the animation step.\n *\n * @usageNotes\n * Call within an animation `sequence()`, `{@link animations/group group()}`, or\n * `transition()` call to specify an animation step\n * that applies given style data to the parent animation for a given amount of time.\n *\n * ### Syntax Examples\n * **Timing examples**\n *\n * The following examples show various `timings` specifications.\n * - `animate(500)` : Duration is 500 milliseconds.\n * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n * milliseconds, easing according to a bezier curve.\n *\n * **Style examples**\n *\n * The following example calls `style()` to set a single CSS style.\n * ```typescript\n * animate(500, style({ background: \"red\" }))\n * ```\n * The following example calls `keyframes()` to set a CSS style\n * to different values for successive keyframes.\n * ```typescript\n * animate(500, keyframes(\n *  [\n *   style({ background: \"blue\" }),\n *   style({ background: \"red\" })\n *  ])\n * ```\n *\n * @publicApi\n */\nfunction animate(timings, styles = null) {\n  return {\n    type: AnimationMetadataType.Animate,\n    styles,\n    timings\n  };\n}\n/**\n * @description Defines a list of animation steps to be run in parallel.\n *\n * @param steps An array of animation step objects.\n * - When steps are defined by `style()` or `animate()`\n * function calls, each call within the group is executed instantly.\n * - To specify offset styles to be applied at a later time, define steps with\n * `keyframes()`, or use `animate()` calls with a delay value.\n * For example:\n *\n * ```typescript\n * group([\n *   animate(\"1s\", style({ background: \"black\" })),\n *   animate(\"2s\", style({ color: \"white\" }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the group data.\n *\n * @usageNotes\n * Grouped animations are useful when a series of styles must be\n * animated at different starting times and closed off at different ending times.\n *\n * When called within a `sequence()` or a\n * `transition()` call, does not continue to the next\n * instruction until all of the inner animation steps have completed.\n *\n * @publicApi\n */\nfunction group(steps, options = null) {\n  return {\n    type: AnimationMetadataType.Group,\n    steps,\n    options\n  };\n}\n/**\n * Defines a list of animation steps to be run sequentially, one by one.\n *\n * @param steps An array of animation step objects.\n * - Steps defined by `style()` calls apply the styling data immediately.\n * - Steps defined by `animate()` calls apply the styling data over time\n *   as specified by the timing data.\n *\n * ```typescript\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(\"1s\", style({ opacity: 1 }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the sequence data.\n *\n * @usageNotes\n * When you pass an array of steps to a\n * `transition()` call, the steps run sequentially by default.\n * Compare this to the `{@link animations/group group()}` call, which runs animation steps in\n *parallel.\n *\n * When a sequence is used within a `{@link animations/group group()}` or a `transition()` call,\n * execution continues to the next instruction only after each of the inner animation\n * steps have completed.\n *\n * @publicApi\n **/\nfunction sequence(steps, options = null) {\n  return {\n    type: AnimationMetadataType.Sequence,\n    steps,\n    options\n  };\n}\n/**\n * Declares a key/value object containing CSS properties/styles that\n * can then be used for an animation [`state`](api/animations/state), within an animation\n *`sequence`, or as styling data for calls to `animate()` and `keyframes()`.\n *\n * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n * The value can be any of the following:\n * - A key-value style pair associating a CSS property with a value.\n * - An array of key-value style pairs.\n * - An asterisk (*), to use auto-styling, where styles are derived from the element\n * being animated and applied to the animation when it starts.\n *\n * Auto-styling can be used to define a state that depends on layout or other\n * environmental factors.\n *\n * @return An object that encapsulates the style data.\n *\n * @usageNotes\n * The following examples create animation styles that collect a set of\n * CSS property values:\n *\n * ```typescript\n * // string values for CSS properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical pixel values\n * style({ width: 100, height: 0 })\n * ```\n *\n * The following example uses auto-styling to allow an element to animate from\n * a height of 0 up to its full height:\n *\n * ```\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * @publicApi\n **/\nfunction style(tokens) {\n  return {\n    type: AnimationMetadataType.Style,\n    styles: tokens,\n    offset: null\n  };\n}\n/**\n * Declares an animation state within a trigger attached to an element.\n *\n * @param name One or more names for the defined state in a comma-separated string.\n * The following reserved state names can be supplied to define a style for specific use\n * cases:\n *\n * - `void` You can associate styles with this name to be used when\n * the element is detached from the application. For example, when an `ngIf` evaluates\n * to false, the state of the associated element is void.\n *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n * to be used as the fallback when the state that is being animated is not declared\n * within the trigger.\n *\n * @param styles A set of CSS styles associated with this state, created using the\n * `style()` function.\n * This set of styles persists on the element once the state has been reached.\n * @param options Parameters that can be passed to the state when it is invoked.\n * 0 or more key-value pairs.\n * @return An object that encapsulates the new state data.\n *\n * @usageNotes\n * Use the `trigger()` function to register states to an animation trigger.\n * Use the `transition()` function to animate between states.\n * When a state is active within a component, its associated styles persist on the element,\n * even when the animation ends.\n *\n * @publicApi\n **/\nfunction state(name, styles, options) {\n  return {\n    type: AnimationMetadataType.State,\n    name,\n    styles,\n    options\n  };\n}\n/**\n * Defines a set of animation styles, associating each style with an optional `offset` value.\n *\n * @param steps A set of animation styles with optional offset data.\n * The optional `offset` value for a style specifies a percentage of the total animation\n * time at which that style is applied.\n * @returns An object that encapsulates the keyframes data.\n *\n * @usageNotes\n * Use with the `animate()` call. Instead of applying animations\n * from the current state\n * to the destination state, keyframes describe how each style entry is applied and at what point\n * within the animation arc.\n * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n *\n * ### Usage\n *\n * In the following example, the offset values describe\n * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n * blue when 20% of the total time has elapsed.\n *\n * ```typescript\n * // the provided offset values\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * If there are no `offset` values specified in the style entries, the offsets\n * are calculated automatically.\n *\n * ```typescript\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n *```\n\n * @publicApi\n */\nfunction keyframes(steps) {\n  return {\n    type: AnimationMetadataType.Keyframes,\n    steps\n  };\n}\n/**\n * Declares an animation transition which is played when a certain specified condition is met.\n *\n * @param stateChangeExpr A string with a specific format or a function that specifies when the\n * animation transition should occur (see [State Change Expression](#state-change-expression)).\n *\n * @param steps One or more animation objects that represent the animation's instructions.\n *\n * @param options An options object that can be used to specify a delay for the animation or provide\n * custom parameters for it.\n *\n * @returns An object that encapsulates the transition data.\n *\n * @usageNotes\n *\n * ### State Change Expression\n *\n * The State Change Expression instructs Angular when to run the transition's animations, it can\n *either be\n *  - a string with a specific syntax\n *  - or a function that compares the previous and current state (value of the expression bound to\n *    the element's trigger) and returns `true` if the transition should occur or `false` otherwise\n *\n * The string format can be:\n *  - `fromState => toState`, which indicates that the transition's animations should occur then the\n *    expression bound to the trigger's element goes from `fromState` to `toState`\n *\n *    _Example:_\n *      ```typescript\n *        transition('open => closed', animate('.5s ease-out', style({ height: 0 }) ))\n *      ```\n *\n *  - `fromState <=> toState`, which indicates that the transition's animations should occur then\n *    the expression bound to the trigger's element goes from `fromState` to `toState` or vice versa\n *\n *    _Example:_\n *      ```typescript\n *        transition('enabled <=> disabled', animate('1s cubic-bezier(0.8,0.3,0,1)'))\n *      ```\n *\n *  - `:enter`/`:leave`, which indicates that the transition's animations should occur when the\n *    element enters or exists the DOM\n *\n *    _Example:_\n *      ```typescript\n *        transition(':enter', [\n *          style({ opacity: 0 }),\n *          animate('500ms', style({ opacity: 1 }))\n *        ])\n *      ```\n *\n *  - `:increment`/`:decrement`, which indicates that the transition's animations should occur when\n *    the numerical expression bound to the trigger's element has increased in value or decreased\n *\n *    _Example:_\n *      ```typescript\n *        transition(':increment', query('@counter', animateChild()))\n *      ```\n *\n *  - a sequence of any of the above divided by commas, which indicates that transition's animations\n *    should occur whenever one of the state change expressions matches\n *\n *    _Example:_\n *      ```typescript\n *        transition(':increment, * => enabled, :enter', animate('1s ease', keyframes([\n *          style({ transform: 'scale(1)', offset: 0}),\n *          style({ transform: 'scale(1.1)', offset: 0.7}),\n *          style({ transform: 'scale(1)', offset: 1})\n *        ]))),\n *      ```\n *\n * Also note that in such context:\n *  - `void` can be used to indicate the absence of the element\n *  - asterisks can be used as wildcards that match any state\n *  - (as a consequence of the above, `void => *` is equivalent to `:enter` and `* => void` is\n *    equivalent to `:leave`)\n *  - `true` and `false` also match expression values of `1` and `0` respectively (but do not match\n *    _truthy_ and _falsy_ values)\n *\n * <div class=\"alert is-helpful\">\n *\n *  Be careful about entering end leaving elements as their transitions present a common\n *  pitfall for developers.\n *\n *  Note that when an element with a trigger enters the DOM its `:enter` transition always\n *  gets executed, but its `:leave` transition will not be executed if the element is removed\n *  alongside its parent (as it will be removed \"without warning\" before its transition has\n *  a chance to be executed, the only way that such transition can occur is if the element\n *  is exiting the DOM on its own).\n *\n *\n * </div>\n *\n * ### Animating to a Final State\n *\n * If the final step in a transition is a call to `animate()` that uses a timing value\n * with no `style` data, that step is automatically considered the final animation arc,\n * for the element to reach the final state, in such case Angular automatically adds or removes\n * CSS styles to ensure that the element is in the correct final state.\n *\n *\n * ### Usage Examples\n *\n *  - Transition animations applied based on\n *    the trigger's expression value\n *\n *   ```HTML\n *   <div [@myAnimationTrigger]=\"myStatusExp\">\n *    ...\n *   </div>\n *   ```\n *\n *   ```typescript\n *   trigger(\"myAnimationTrigger\", [\n *     ..., // states\n *     transition(\"on => off, open => closed\", animate(500)),\n *     transition(\"* <=> error\", query('.indicator', animateChild()))\n *   ])\n *   ```\n *\n *  - Transition animations applied based on custom logic dependent\n *    on the trigger's expression value and provided parameters\n *\n *    ```HTML\n *    <div [@myAnimationTrigger]=\"{\n *     value: stepName,\n *     params: { target: currentTarget }\n *    }\">\n *     ...\n *    </div>\n *    ```\n *\n *    ```typescript\n *    trigger(\"myAnimationTrigger\", [\n *      ..., // states\n *      transition(\n *        (fromState, toState, _element, params) =>\n *          ['firststep', 'laststep'].includes(fromState.toLowerCase())\n *          && toState === params?.['target'],\n *        animate('1s')\n *      )\n *    ])\n *    ```\n *\n * @publicApi\n **/\nfunction transition(stateChangeExpr, steps, options = null) {\n  return {\n    type: AnimationMetadataType.Transition,\n    expr: stateChangeExpr,\n    animation: steps,\n    options\n  };\n}\n/**\n * Produces a reusable animation that can be invoked in another animation or sequence,\n * by calling the `useAnimation()` function.\n *\n * @param steps One or more animation objects, as returned by the `animate()`\n * or `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional developer-defined parameters.\n * Provided values for additional parameters are used as defaults,\n * and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the animation data.\n *\n * @usageNotes\n * The following example defines a reusable animation, providing some default parameter\n * values.\n *\n * ```typescript\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *   style({ opacity: '{{ end }}'}))\n *   ],\n *   { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * The following invokes the defined animation with a call to `useAnimation()`,\n * passing in override parameter values.\n *\n * ```js\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If any of the passed-in parameter values are missing from this call,\n * the default values are used. If one or more parameter values are missing before a step is\n * animated, `useAnimation()` throws an error.\n *\n * @publicApi\n */\nfunction animation(steps, options = null) {\n  return {\n    type: AnimationMetadataType.Reference,\n    animation: steps,\n    options\n  };\n}\n/**\n * Executes a queried inner animation element within an animation sequence.\n *\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional override values for developer-defined parameters.\n * @return An object that encapsulates the child animation data.\n *\n * @usageNotes\n * Each time an animation is triggered in Angular, the parent animation\n * has priority and any child animations are blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations, and run them using this function.\n *\n * Note that this feature is designed to be used with `query()` and it will only work\n * with animations that are assigned using the Angular animation library. CSS keyframes\n * and transitions are not handled by this API.\n *\n * @publicApi\n */\nfunction animateChild(options = null) {\n  return {\n    type: AnimationMetadataType.AnimateChild,\n    options\n  };\n}\n/**\n * Starts a reusable animation that is created using the `animation()` function.\n *\n * @param animation The reusable animation to start.\n * @param options An options object that can contain a delay value for the start of\n * the animation, and additional override values for developer-defined parameters.\n * @return An object that contains the animation parameters.\n *\n * @publicApi\n */\nfunction useAnimation(animation, options = null) {\n  return {\n    type: AnimationMetadataType.AnimateRef,\n    animation,\n    options\n  };\n}\n/**\n * Finds one or more inner elements within the current element that is\n * being animated within a sequence. Use with `animate()`.\n *\n * @param selector The element to query, or a set of elements that contain Angular-specific\n * characteristics, specified with one or more of the following tokens.\n *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements (not\n *     all elements can be queried via these tokens, see\n *     [Entering and Leaving Elements](#entering-and-leaving-elements))\n *  - `query(\":animating\")` : Query all currently animating elements.\n *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n *  - `query(\":self\")` : Include the current element into the animation sequence.\n *\n * @param animation One or more animation steps to apply to the queried element or elements.\n * An array is treated as an animation sequence.\n * @param options An options object. Use the 'limit' field to limit the total number of\n * items to collect.\n * @return An object that encapsulates the query data.\n *\n * @usageNotes\n *\n * ### Multiple Tokens\n *\n * Tokens can be merged into a combined query selector string. For example:\n *\n * ```typescript\n *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n * ```\n *\n * The `query()` function collects multiple elements and works internally by using\n * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n * the total number of items to be collected. For example:\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * By default, throws an error when zero items are found. Set the\n * `optional` flag to ignore this error. For example:\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Entering and Leaving Elements\n *\n * Not all elements can be queried via the `:enter` and `:leave` tokens, the only ones\n * that can are those that Angular assumes can enter/leave based on their own logic\n * (if their insertion/removal is simply a consequence of that of their parent they\n * should be queried via a different token in their parent's `:enter`/`:leave` transitions).\n *\n * The only elements Angular assumes can enter/leave based on their own logic (thus the only\n * ones that can be queried via the `:enter` and `:leave` tokens) are:\n *  - Those inserted dynamically (via `ViewContainerRef`)\n *  - Those that have a structural directive (which, under the hood, are a subset of the above ones)\n *\n * <div class=\"alert is-helpful\">\n *\n *  Note that elements will be successfully queried via `:enter`/`:leave` even if their\n *  insertion/removal is not done manually via `ViewContainerRef`or caused by their structural\n *  directive (e.g. they enter/exit alongside their parent).\n *\n * </div>\n *\n * <div class=\"alert is-important\">\n *\n *  There is an exception to what previously mentioned, besides elements entering/leaving based on\n *  their own logic, elements with an animation trigger can always be queried via `:leave` when\n * their parent is also leaving.\n *\n * </div>\n *\n * ### Usage Example\n *\n * The following example queries for inner elements and animates them\n * individually using `animate()`.\n *\n * ```typescript\n * @Component({\n *   selector: 'inner',\n *   template: `\n *     <div [@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 }))),\n *        query('.content', animate(1000, style({ opacity: 1 }))),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction query(selector, animation, options = null) {\n  return {\n    type: AnimationMetadataType.Query,\n    selector,\n    animation,\n    options\n  };\n}\n/**\n * Use within an animation `query()` call to issue a timing gap after\n * each queried item is animated.\n *\n * @param timings A delay value.\n * @param animation One ore more animation steps.\n * @returns An object that encapsulates the stagger data.\n *\n * @usageNotes\n * In the following example, a container element wraps a list of items stamped out\n * by an `ngFor`. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * Each time items are added, the opacity fade-in animation runs,\n * and each removed item is faded out.\n * When either of these animations occur, the stagger effect is\n * applied after each item's animation is started.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n *\n * Here is the component code:\n *\n * ```typescript\n * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n * @Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *     ...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *    }\n *  }\n * ```\n *\n * Here is the animation trigger code:\n *\n * ```typescript\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * @publicApi\n */\nfunction stagger(timings, animation) {\n  return {\n    type: AnimationMetadataType.Stagger,\n    timings,\n    animation\n  };\n}\n\n/**\n * An injectable service that produces an animation sequence programmatically within an\n * Angular component or directive.\n * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n *\n * @usageNotes\n *\n * To use this service, add it to your component or directive as a dependency.\n * The service is instantiated along with your component.\n *\n * Apps do not typically need to create their own animation players, but if you\n * do need to, follow these steps:\n *\n * 1. Use the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code> method\n * to create a programmatic animation. The method returns an `AnimationFactory` instance.\n *\n * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n *\n * 3. Use the player object to control the animation programmatically.\n *\n * For example:\n *\n * ```ts\n * // import the service from BrowserAnimationsModule\n * import {AnimationBuilder} from '@angular/animations';\n * // require the service as a dependency\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first define a reusable animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // use the returned factory object to create a player\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass AnimationBuilder {\n  static {\n    this.ɵfac = function AnimationBuilder_Factory(t) {\n      return new (t || AnimationBuilder)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AnimationBuilder,\n      factory: () => (() => inject(BrowserAnimationBuilder))(),\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(BrowserAnimationBuilder)\n    }]\n  }], null, null);\n})();\n/**\n * A factory object returned from the\n * <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n * method.\n *\n * @publicApi\n */\nclass AnimationFactory {}\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  constructor(rootRenderer, doc) {\n    super();\n    this.animationModuleType = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._nextAnimationId = 0;\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    if (this.animationModuleType === null && !isAnimationRenderer(this._renderer)) {\n      // We only support AnimationRenderer & DynamicDelegationRenderer for this AnimationBuilder\n      throw new ɵRuntimeError(3600 /* RuntimeErrorCode.BROWSER_ANIMATION_BUILDER_INJECTED_WITHOUT_ANIMATIONS */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Angular detected that the `AnimationBuilder` was injected, but animation support was not enabled. ' + 'Please make sure that you enable animations in your application by calling `provideAnimations()` or `provideAnimationsAsync()` function.');\n    }\n  }\n  build(animation) {\n    const id = this._nextAnimationId;\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n  static {\n    this.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n      return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserAnimationBuilder,\n      factory: BrowserAnimationBuilder.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n    this._command('create', options);\n  }\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n  _command(command, ...args) {\n    issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n  init() {\n    this._command('init');\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    this._command('play');\n    this._started = true;\n  }\n  pause() {\n    this._command('pause');\n  }\n  restart() {\n    this._command('restart');\n  }\n  finish() {\n    this._command('finish');\n  }\n  destroy() {\n    this._command('destroy');\n  }\n  reset() {\n    this._command('reset');\n    this._started = false;\n  }\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n  getPosition() {\n    return unwrapAnimationRenderer(this._renderer)?.engine?.players[this.id]?.getPosition() ?? 0;\n  }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n/**\n * The following 2 methods cannot reference their correct types (AnimationRenderer &\n * DynamicDelegationRenderer) since this would introduce a import cycle.\n */\nfunction unwrapAnimationRenderer(renderer) {\n  const type = renderer.ɵtype;\n  if (type === 0 /* AnimationRendererType.Regular */) {\n    return renderer;\n  } else if (type === 1 /* AnimationRendererType.Delegated */) {\n    return renderer.animationRenderer;\n  }\n  return null;\n}\nfunction isAnimationRenderer(renderer) {\n  const type = renderer.ɵtype;\n  return type === 0 /* AnimationRendererType.Regular */ || type === 1 /* AnimationRendererType.Delegated */;\n}\n\n/**\n * An empty programmatic controller for reusable animations.\n * Used internally when animations are disabled, to avoid\n * checking for the null case when an animation player is expected.\n *\n * @see {@link animate}\n * @see {@link AnimationPlayer}\n * @see {@link ɵAnimationGroupPlayer AnimationGroupPlayer}\n *\n * @publicApi\n */\nclass NoopAnimationPlayer {\n  constructor(duration = 0, delay = 0) {\n    this._onDoneFns = [];\n    this._onStartFns = [];\n    this._onDestroyFns = [];\n    this._originalOnDoneFns = [];\n    this._originalOnStartFns = [];\n    this._started = false;\n    this._destroyed = false;\n    this._finished = false;\n    this._position = 0;\n    this.parentPlayer = null;\n    this.totalTime = duration + delay;\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  hasStarted() {\n    return this._started;\n  }\n  init() {}\n  play() {\n    if (!this.hasStarted()) {\n      this._onStart();\n      this.triggerMicrotask();\n    }\n    this._started = true;\n  }\n  /** @internal */\n  triggerMicrotask() {\n    queueMicrotask(() => this._onFinish());\n  }\n  _onStart() {\n    this._onStartFns.forEach(fn => fn());\n    this._onStartFns = [];\n  }\n  pause() {}\n  restart() {}\n  finish() {\n    this._onFinish();\n  }\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      if (!this.hasStarted()) {\n        this._onStart();\n      }\n      this.finish();\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  reset() {\n    this._started = false;\n    this._finished = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  setPosition(position) {\n    this._position = this.totalTime ? position * this.totalTime : 1;\n  }\n  getPosition() {\n    return this.totalTime ? this._position / this.totalTime : 1;\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\n\n/**\n * A programmatic controller for a group of reusable animations.\n * Used internally to control animations.\n *\n * @see {@link AnimationPlayer}\n * @see {@link animations/group group}\n *\n */\nclass AnimationGroupPlayer {\n  constructor(_players) {\n    this._onDoneFns = [];\n    this._onStartFns = [];\n    this._finished = false;\n    this._started = false;\n    this._destroyed = false;\n    this._onDestroyFns = [];\n    this.parentPlayer = null;\n    this.totalTime = 0;\n    this.players = _players;\n    let doneCount = 0;\n    let destroyCount = 0;\n    let startCount = 0;\n    const total = this.players.length;\n    if (total == 0) {\n      queueMicrotask(() => this._onFinish());\n    } else {\n      this.players.forEach(player => {\n        player.onDone(() => {\n          if (++doneCount == total) {\n            this._onFinish();\n          }\n        });\n        player.onDestroy(() => {\n          if (++destroyCount == total) {\n            this._onDestroy();\n          }\n        });\n        player.onStart(() => {\n          if (++startCount == total) {\n            this._onStart();\n          }\n        });\n      });\n    }\n    this.totalTime = this.players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  init() {\n    this.players.forEach(player => player.init());\n  }\n  onStart(fn) {\n    this._onStartFns.push(fn);\n  }\n  _onStart() {\n    if (!this.hasStarted()) {\n      this._started = true;\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n    }\n  }\n  onDone(fn) {\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  hasStarted() {\n    return this._started;\n  }\n  play() {\n    if (!this.parentPlayer) {\n      this.init();\n    }\n    this._onStart();\n    this.players.forEach(player => player.play());\n  }\n  pause() {\n    this.players.forEach(player => player.pause());\n  }\n  restart() {\n    this.players.forEach(player => player.restart());\n  }\n  finish() {\n    this._onFinish();\n    this.players.forEach(player => player.finish());\n  }\n  destroy() {\n    this._onDestroy();\n  }\n  _onDestroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._onFinish();\n      this.players.forEach(player => player.destroy());\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  reset() {\n    this.players.forEach(player => player.reset());\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n  }\n  setPosition(p) {\n    const timeAtPosition = p * this.totalTime;\n    this.players.forEach(player => {\n      const position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n      player.setPosition(position);\n    });\n  }\n  getPosition() {\n    const longestPlayer = this.players.reduce((longestSoFar, player) => {\n      const newPlayerIsLongest = longestSoFar === null || player.totalTime > longestSoFar.totalTime;\n      return newPlayerIsLongest ? player : longestSoFar;\n    }, null);\n    return longestPlayer != null ? longestPlayer.getPosition() : 0;\n  }\n  beforeDestroy() {\n    this.players.forEach(player => {\n      if (player.beforeDestroy) {\n        player.beforeDestroy();\n      }\n    });\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\nconst ɵPRE_STYLE = '!';\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTO_STYLE, AnimationBuilder, AnimationFactory, AnimationMetadataType, NoopAnimationPlayer, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, AnimationGroupPlayer as ɵAnimationGroupPlayer, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, ɵPRE_STYLE };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "Injectable", "ANIMATION_MODULE_TYPE", "ViewEncapsulation", "ɵRuntimeError", "Inject", "AnimationMetadataType", "AUTO_STYLE", "trigger", "name", "definitions", "type", "<PERSON><PERSON>", "options", "animate", "timings", "styles", "Animate", "group", "steps", "Group", "sequence", "Sequence", "style", "tokens", "Style", "offset", "state", "State", "keyframes", "Keyframes", "transition", "stateChangeExpr", "Transition", "expr", "animation", "Reference", "animate<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useAnimation", "AnimateRef", "query", "selector", "Query", "stagger", "Stagger", "AnimationBuilder", "ɵfac", "AnimationBuilder_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "BrowserAnimationBuilder", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "useFactory", "AnimationFactory", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "doc", "animationModuleType", "optional", "_nextAnimationId", "typeData", "id", "encapsulation", "None", "data", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "body", "isAnimationRenderer", "build", "entry", "Array", "isArray", "issueAnimationCommand", "BrowserAnimationFactory", "BrowserAnimationBuilder_Factory", "ɵɵinject", "RendererFactory2", "Document", "decorators", "_id", "create", "element", "RendererAnimationPlayer", "parentPlayer", "_started", "totalTime", "_command", "_listen", "eventName", "callback", "listen", "command", "onDone", "fn", "onStart", "onDestroy", "init", "hasStarted", "play", "pause", "restart", "finish", "destroy", "reset", "setPosition", "p", "getPosition", "unwrapAnimation<PERSON><PERSON><PERSON>", "engine", "players", "renderer", "setProperty", "ɵtype", "<PERSON><PERSON><PERSON><PERSON>", "NoopAnimationPlayer", "duration", "delay", "_onDoneFns", "_onStartFns", "_onDestroyFns", "_originalOnDoneFns", "_originalOnStartFns", "_destroyed", "_finished", "_position", "_onFinish", "for<PERSON>ach", "push", "_onStart", "triggerMicrotask", "queueMicrotask", "position", "triggerCallback", "phaseName", "methods", "length", "AnimationGroupPlayer", "_players", "doneCount", "destroyCount", "startCount", "total", "player", "_onD<PERSON>roy", "reduce", "time", "Math", "max", "timeAtPosition", "min", "longestPlayer", "longestSoFar", "newPlayerIsLongest", "<PERSON><PERSON><PERSON><PERSON>", "ɵPRE_STYLE", "ɵAnimationGroupPlayer", "ɵBrowserAnimationBuilder"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/@angular/animations/fesm2022/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, ANIMATION_MODULE_TYPE, ViewEncapsulation, ɵRuntimeError, Inject } from '@angular/core';\n\n/**\n * @description Constants for the categories of parameters that can be defined for animations.\n *\n * A corresponding function defines a set of parameters for each category, and\n * collects them into a corresponding `AnimationMetadata` object.\n *\n * @publicApi\n */\nvar AnimationMetadataType;\n(function (AnimationMetadataType) {\n    /**\n     * Associates a named animation state with a set of CSS styles.\n     * See [`state()`](api/animations/state)\n     */\n    AnimationMetadataType[AnimationMetadataType[\"State\"] = 0] = \"State\";\n    /**\n     * Data for a transition from one animation state to another.\n     * See `transition()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Transition\"] = 1] = \"Transition\";\n    /**\n     * Contains a set of animation steps.\n     * See `sequence()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Sequence\"] = 2] = \"Sequence\";\n    /**\n     * Contains a set of animation steps.\n     * See `{@link animations/group group()}`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Group\"] = 3] = \"Group\";\n    /**\n     * Contains an animation step.\n     * See `animate()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Animate\"] = 4] = \"Animate\";\n    /**\n     * Contains a set of animation steps.\n     * See `keyframes()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Keyframes\"] = 5] = \"Keyframes\";\n    /**\n     * Contains a set of CSS property-value pairs into a named style.\n     * See `style()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Style\"] = 6] = \"Style\";\n    /**\n     * Associates an animation with an entry trigger that can be attached to an element.\n     * See `trigger()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Trigger\"] = 7] = \"Trigger\";\n    /**\n     * Contains a re-usable animation.\n     * See `animation()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Reference\"] = 8] = \"Reference\";\n    /**\n     * Contains data to use in executing child animations returned by a query.\n     * See `animateChild()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"AnimateChild\"] = 9] = \"AnimateChild\";\n    /**\n     * Contains animation parameters for a re-usable animation.\n     * See `useAnimation()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"AnimateRef\"] = 10] = \"AnimateRef\";\n    /**\n     * Contains child-animation query data.\n     * See `query()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Query\"] = 11] = \"Query\";\n    /**\n     * Contains data for staggering an animation sequence.\n     * See `stagger()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Stagger\"] = 12] = \"Stagger\";\n})(AnimationMetadataType || (AnimationMetadataType = {}));\n/**\n * Specifies automatic styling.\n *\n * @publicApi\n */\nconst AUTO_STYLE = '*';\n/**\n * Creates a named animation trigger, containing a  list of [`state()`](api/animations/state)\n * and `transition()` entries to be evaluated when the expression\n * bound to the trigger changes.\n *\n * @param name An identifying string.\n * @param definitions  An animation definition object, containing an array of\n * [`state()`](api/animations/state) and `transition()` declarations.\n *\n * @return An object that encapsulates the trigger data.\n *\n * @usageNotes\n * Define an animation trigger in the `animations` section of `@Component` metadata.\n * In the template, reference the trigger by name and bind it to a trigger expression that\n * evaluates to a defined animation state, using the following format:\n *\n * `[@triggerName]=\"expression\"`\n *\n * Animation trigger bindings convert all values to strings, and then match the\n * previous and current values against any linked transitions.\n * Booleans can be specified as `1` or `true` and `0` or `false`.\n *\n * ### Usage Example\n *\n * The following example creates an animation trigger reference based on the provided\n * name value.\n * The provided animation value is expected to be an array consisting of state and\n * transition declarations.\n *\n * ```typescript\n * @Component({\n *   selector: \"my-component\",\n *   templateUrl: \"my-component-tpl.html\",\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component makes use of the defined trigger\n * by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ### Using an inline function\n * The `transition` animation method also supports reading an inline function which can decide\n * if its associated animation should be run.\n *\n * ```typescript\n * // this method is run each time the `myAnimationTrigger` trigger value changes.\n * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n string]: any}): boolean {\n *   // notice that `element` and `params` are also available here\n *   return toState == 'yes-please-animate';\n * }\n *\n * @Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger('myAnimationTrigger', [\n *       transition(myInlineMatcherFn, [\n *         // the animation sequence code\n *       ]),\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"yes-please-animate\";\n * }\n * ```\n *\n * ### Disabling Animations\n * When true, the special animation control binding `@.disabled` binding prevents\n * all animations from rendering.\n * Place the  `@.disabled` binding on an element to disable\n * animations on the element itself, as well as any inner animation triggers\n * within the element.\n *\n * The following example shows how to use this feature:\n *\n * ```typescript\n * @Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [@.disabled]=\"isDisabled\">\n *       <div [@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n * along with any inner animations.\n *\n * ### Disable animations application-wide\n * When an area of the template is set to have animations disabled,\n * **all** inner components have their animations disabled as well.\n * This means that you can disable all animations for an app\n * by placing a host binding set on `@.disabled` on the topmost Angular component.\n *\n * ```typescript\n * import {Component, HostBinding} from '@angular/core';\n *\n * @Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   @HostBinding('@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### Overriding disablement of inner animations\n * Despite inner animations being disabled, a parent animation can `query()`\n * for inner elements located in disabled areas of the template and still animate\n * them if needed. This is also the case for when a sub animation is\n * queried by a parent and then later animated using `animateChild()`.\n *\n * ### Detecting when an animation is disabled\n * If a region of the DOM (or the entire application) has its animations disabled, the animation\n * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n * an instance of an `AnimationEvent`. If animations are disabled,\n * the `.disabled` flag on the event is true.\n *\n * @publicApi\n */\nfunction trigger(name, definitions) {\n    return { type: AnimationMetadataType.Trigger, name, definitions, options: {} };\n}\n/**\n * Defines an animation step that combines styling information with timing information.\n *\n * @param timings Sets `AnimateTimings` for the parent animation.\n * A string in the format \"duration [delay] [easing]\".\n *  - Duration and delay are expressed as a number and optional time unit,\n * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n * The default unit is milliseconds.\n *  - The easing value controls how the animation accelerates and decelerates\n * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n * `ease-in-out`, or a `cubic-bezier()` function call.\n * If not supplied, no easing is applied.\n *\n * For example, the string \"1s 100ms ease-out\" specifies a duration of\n * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n * which decelerates near the end of the duration.\n * @param styles Sets AnimationStyles for the parent animation.\n * A function call to either `style()` or `keyframes()`\n * that returns a collection of CSS style entries to be applied to the parent animation.\n * When null, uses the styles from the destination state.\n * This is useful when describing an animation step that will complete an animation;\n * see \"Animating to the final state\" in `transitions()`.\n * @returns An object that encapsulates the animation step.\n *\n * @usageNotes\n * Call within an animation `sequence()`, `{@link animations/group group()}`, or\n * `transition()` call to specify an animation step\n * that applies given style data to the parent animation for a given amount of time.\n *\n * ### Syntax Examples\n * **Timing examples**\n *\n * The following examples show various `timings` specifications.\n * - `animate(500)` : Duration is 500 milliseconds.\n * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n * milliseconds, easing according to a bezier curve.\n *\n * **Style examples**\n *\n * The following example calls `style()` to set a single CSS style.\n * ```typescript\n * animate(500, style({ background: \"red\" }))\n * ```\n * The following example calls `keyframes()` to set a CSS style\n * to different values for successive keyframes.\n * ```typescript\n * animate(500, keyframes(\n *  [\n *   style({ background: \"blue\" }),\n *   style({ background: \"red\" })\n *  ])\n * ```\n *\n * @publicApi\n */\nfunction animate(timings, styles = null) {\n    return { type: AnimationMetadataType.Animate, styles, timings };\n}\n/**\n * @description Defines a list of animation steps to be run in parallel.\n *\n * @param steps An array of animation step objects.\n * - When steps are defined by `style()` or `animate()`\n * function calls, each call within the group is executed instantly.\n * - To specify offset styles to be applied at a later time, define steps with\n * `keyframes()`, or use `animate()` calls with a delay value.\n * For example:\n *\n * ```typescript\n * group([\n *   animate(\"1s\", style({ background: \"black\" })),\n *   animate(\"2s\", style({ color: \"white\" }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the group data.\n *\n * @usageNotes\n * Grouped animations are useful when a series of styles must be\n * animated at different starting times and closed off at different ending times.\n *\n * When called within a `sequence()` or a\n * `transition()` call, does not continue to the next\n * instruction until all of the inner animation steps have completed.\n *\n * @publicApi\n */\nfunction group(steps, options = null) {\n    return { type: AnimationMetadataType.Group, steps, options };\n}\n/**\n * Defines a list of animation steps to be run sequentially, one by one.\n *\n * @param steps An array of animation step objects.\n * - Steps defined by `style()` calls apply the styling data immediately.\n * - Steps defined by `animate()` calls apply the styling data over time\n *   as specified by the timing data.\n *\n * ```typescript\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(\"1s\", style({ opacity: 1 }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the sequence data.\n *\n * @usageNotes\n * When you pass an array of steps to a\n * `transition()` call, the steps run sequentially by default.\n * Compare this to the `{@link animations/group group()}` call, which runs animation steps in\n *parallel.\n *\n * When a sequence is used within a `{@link animations/group group()}` or a `transition()` call,\n * execution continues to the next instruction only after each of the inner animation\n * steps have completed.\n *\n * @publicApi\n **/\nfunction sequence(steps, options = null) {\n    return { type: AnimationMetadataType.Sequence, steps, options };\n}\n/**\n * Declares a key/value object containing CSS properties/styles that\n * can then be used for an animation [`state`](api/animations/state), within an animation\n *`sequence`, or as styling data for calls to `animate()` and `keyframes()`.\n *\n * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n * The value can be any of the following:\n * - A key-value style pair associating a CSS property with a value.\n * - An array of key-value style pairs.\n * - An asterisk (*), to use auto-styling, where styles are derived from the element\n * being animated and applied to the animation when it starts.\n *\n * Auto-styling can be used to define a state that depends on layout or other\n * environmental factors.\n *\n * @return An object that encapsulates the style data.\n *\n * @usageNotes\n * The following examples create animation styles that collect a set of\n * CSS property values:\n *\n * ```typescript\n * // string values for CSS properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical pixel values\n * style({ width: 100, height: 0 })\n * ```\n *\n * The following example uses auto-styling to allow an element to animate from\n * a height of 0 up to its full height:\n *\n * ```\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * @publicApi\n **/\nfunction style(tokens) {\n    return { type: AnimationMetadataType.Style, styles: tokens, offset: null };\n}\n/**\n * Declares an animation state within a trigger attached to an element.\n *\n * @param name One or more names for the defined state in a comma-separated string.\n * The following reserved state names can be supplied to define a style for specific use\n * cases:\n *\n * - `void` You can associate styles with this name to be used when\n * the element is detached from the application. For example, when an `ngIf` evaluates\n * to false, the state of the associated element is void.\n *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n * to be used as the fallback when the state that is being animated is not declared\n * within the trigger.\n *\n * @param styles A set of CSS styles associated with this state, created using the\n * `style()` function.\n * This set of styles persists on the element once the state has been reached.\n * @param options Parameters that can be passed to the state when it is invoked.\n * 0 or more key-value pairs.\n * @return An object that encapsulates the new state data.\n *\n * @usageNotes\n * Use the `trigger()` function to register states to an animation trigger.\n * Use the `transition()` function to animate between states.\n * When a state is active within a component, its associated styles persist on the element,\n * even when the animation ends.\n *\n * @publicApi\n **/\nfunction state(name, styles, options) {\n    return { type: AnimationMetadataType.State, name, styles, options };\n}\n/**\n * Defines a set of animation styles, associating each style with an optional `offset` value.\n *\n * @param steps A set of animation styles with optional offset data.\n * The optional `offset` value for a style specifies a percentage of the total animation\n * time at which that style is applied.\n * @returns An object that encapsulates the keyframes data.\n *\n * @usageNotes\n * Use with the `animate()` call. Instead of applying animations\n * from the current state\n * to the destination state, keyframes describe how each style entry is applied and at what point\n * within the animation arc.\n * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n *\n * ### Usage\n *\n * In the following example, the offset values describe\n * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n * blue when 20% of the total time has elapsed.\n *\n * ```typescript\n * // the provided offset values\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * If there are no `offset` values specified in the style entries, the offsets\n * are calculated automatically.\n *\n * ```typescript\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n *```\n\n * @publicApi\n */\nfunction keyframes(steps) {\n    return { type: AnimationMetadataType.Keyframes, steps };\n}\n/**\n * Declares an animation transition which is played when a certain specified condition is met.\n *\n * @param stateChangeExpr A string with a specific format or a function that specifies when the\n * animation transition should occur (see [State Change Expression](#state-change-expression)).\n *\n * @param steps One or more animation objects that represent the animation's instructions.\n *\n * @param options An options object that can be used to specify a delay for the animation or provide\n * custom parameters for it.\n *\n * @returns An object that encapsulates the transition data.\n *\n * @usageNotes\n *\n * ### State Change Expression\n *\n * The State Change Expression instructs Angular when to run the transition's animations, it can\n *either be\n *  - a string with a specific syntax\n *  - or a function that compares the previous and current state (value of the expression bound to\n *    the element's trigger) and returns `true` if the transition should occur or `false` otherwise\n *\n * The string format can be:\n *  - `fromState => toState`, which indicates that the transition's animations should occur then the\n *    expression bound to the trigger's element goes from `fromState` to `toState`\n *\n *    _Example:_\n *      ```typescript\n *        transition('open => closed', animate('.5s ease-out', style({ height: 0 }) ))\n *      ```\n *\n *  - `fromState <=> toState`, which indicates that the transition's animations should occur then\n *    the expression bound to the trigger's element goes from `fromState` to `toState` or vice versa\n *\n *    _Example:_\n *      ```typescript\n *        transition('enabled <=> disabled', animate('1s cubic-bezier(0.8,0.3,0,1)'))\n *      ```\n *\n *  - `:enter`/`:leave`, which indicates that the transition's animations should occur when the\n *    element enters or exists the DOM\n *\n *    _Example:_\n *      ```typescript\n *        transition(':enter', [\n *          style({ opacity: 0 }),\n *          animate('500ms', style({ opacity: 1 }))\n *        ])\n *      ```\n *\n *  - `:increment`/`:decrement`, which indicates that the transition's animations should occur when\n *    the numerical expression bound to the trigger's element has increased in value or decreased\n *\n *    _Example:_\n *      ```typescript\n *        transition(':increment', query('@counter', animateChild()))\n *      ```\n *\n *  - a sequence of any of the above divided by commas, which indicates that transition's animations\n *    should occur whenever one of the state change expressions matches\n *\n *    _Example:_\n *      ```typescript\n *        transition(':increment, * => enabled, :enter', animate('1s ease', keyframes([\n *          style({ transform: 'scale(1)', offset: 0}),\n *          style({ transform: 'scale(1.1)', offset: 0.7}),\n *          style({ transform: 'scale(1)', offset: 1})\n *        ]))),\n *      ```\n *\n * Also note that in such context:\n *  - `void` can be used to indicate the absence of the element\n *  - asterisks can be used as wildcards that match any state\n *  - (as a consequence of the above, `void => *` is equivalent to `:enter` and `* => void` is\n *    equivalent to `:leave`)\n *  - `true` and `false` also match expression values of `1` and `0` respectively (but do not match\n *    _truthy_ and _falsy_ values)\n *\n * <div class=\"alert is-helpful\">\n *\n *  Be careful about entering end leaving elements as their transitions present a common\n *  pitfall for developers.\n *\n *  Note that when an element with a trigger enters the DOM its `:enter` transition always\n *  gets executed, but its `:leave` transition will not be executed if the element is removed\n *  alongside its parent (as it will be removed \"without warning\" before its transition has\n *  a chance to be executed, the only way that such transition can occur is if the element\n *  is exiting the DOM on its own).\n *\n *\n * </div>\n *\n * ### Animating to a Final State\n *\n * If the final step in a transition is a call to `animate()` that uses a timing value\n * with no `style` data, that step is automatically considered the final animation arc,\n * for the element to reach the final state, in such case Angular automatically adds or removes\n * CSS styles to ensure that the element is in the correct final state.\n *\n *\n * ### Usage Examples\n *\n *  - Transition animations applied based on\n *    the trigger's expression value\n *\n *   ```HTML\n *   <div [@myAnimationTrigger]=\"myStatusExp\">\n *    ...\n *   </div>\n *   ```\n *\n *   ```typescript\n *   trigger(\"myAnimationTrigger\", [\n *     ..., // states\n *     transition(\"on => off, open => closed\", animate(500)),\n *     transition(\"* <=> error\", query('.indicator', animateChild()))\n *   ])\n *   ```\n *\n *  - Transition animations applied based on custom logic dependent\n *    on the trigger's expression value and provided parameters\n *\n *    ```HTML\n *    <div [@myAnimationTrigger]=\"{\n *     value: stepName,\n *     params: { target: currentTarget }\n *    }\">\n *     ...\n *    </div>\n *    ```\n *\n *    ```typescript\n *    trigger(\"myAnimationTrigger\", [\n *      ..., // states\n *      transition(\n *        (fromState, toState, _element, params) =>\n *          ['firststep', 'laststep'].includes(fromState.toLowerCase())\n *          && toState === params?.['target'],\n *        animate('1s')\n *      )\n *    ])\n *    ```\n *\n * @publicApi\n **/\nfunction transition(stateChangeExpr, steps, options = null) {\n    return { type: AnimationMetadataType.Transition, expr: stateChangeExpr, animation: steps, options };\n}\n/**\n * Produces a reusable animation that can be invoked in another animation or sequence,\n * by calling the `useAnimation()` function.\n *\n * @param steps One or more animation objects, as returned by the `animate()`\n * or `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional developer-defined parameters.\n * Provided values for additional parameters are used as defaults,\n * and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the animation data.\n *\n * @usageNotes\n * The following example defines a reusable animation, providing some default parameter\n * values.\n *\n * ```typescript\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *   style({ opacity: '{{ end }}'}))\n *   ],\n *   { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * The following invokes the defined animation with a call to `useAnimation()`,\n * passing in override parameter values.\n *\n * ```js\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If any of the passed-in parameter values are missing from this call,\n * the default values are used. If one or more parameter values are missing before a step is\n * animated, `useAnimation()` throws an error.\n *\n * @publicApi\n */\nfunction animation(steps, options = null) {\n    return { type: AnimationMetadataType.Reference, animation: steps, options };\n}\n/**\n * Executes a queried inner animation element within an animation sequence.\n *\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional override values for developer-defined parameters.\n * @return An object that encapsulates the child animation data.\n *\n * @usageNotes\n * Each time an animation is triggered in Angular, the parent animation\n * has priority and any child animations are blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations, and run them using this function.\n *\n * Note that this feature is designed to be used with `query()` and it will only work\n * with animations that are assigned using the Angular animation library. CSS keyframes\n * and transitions are not handled by this API.\n *\n * @publicApi\n */\nfunction animateChild(options = null) {\n    return { type: AnimationMetadataType.AnimateChild, options };\n}\n/**\n * Starts a reusable animation that is created using the `animation()` function.\n *\n * @param animation The reusable animation to start.\n * @param options An options object that can contain a delay value for the start of\n * the animation, and additional override values for developer-defined parameters.\n * @return An object that contains the animation parameters.\n *\n * @publicApi\n */\nfunction useAnimation(animation, options = null) {\n    return { type: AnimationMetadataType.AnimateRef, animation, options };\n}\n/**\n * Finds one or more inner elements within the current element that is\n * being animated within a sequence. Use with `animate()`.\n *\n * @param selector The element to query, or a set of elements that contain Angular-specific\n * characteristics, specified with one or more of the following tokens.\n *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements (not\n *     all elements can be queried via these tokens, see\n *     [Entering and Leaving Elements](#entering-and-leaving-elements))\n *  - `query(\":animating\")` : Query all currently animating elements.\n *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n *  - `query(\":self\")` : Include the current element into the animation sequence.\n *\n * @param animation One or more animation steps to apply to the queried element or elements.\n * An array is treated as an animation sequence.\n * @param options An options object. Use the 'limit' field to limit the total number of\n * items to collect.\n * @return An object that encapsulates the query data.\n *\n * @usageNotes\n *\n * ### Multiple Tokens\n *\n * Tokens can be merged into a combined query selector string. For example:\n *\n * ```typescript\n *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n * ```\n *\n * The `query()` function collects multiple elements and works internally by using\n * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n * the total number of items to be collected. For example:\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * By default, throws an error when zero items are found. Set the\n * `optional` flag to ignore this error. For example:\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Entering and Leaving Elements\n *\n * Not all elements can be queried via the `:enter` and `:leave` tokens, the only ones\n * that can are those that Angular assumes can enter/leave based on their own logic\n * (if their insertion/removal is simply a consequence of that of their parent they\n * should be queried via a different token in their parent's `:enter`/`:leave` transitions).\n *\n * The only elements Angular assumes can enter/leave based on their own logic (thus the only\n * ones that can be queried via the `:enter` and `:leave` tokens) are:\n *  - Those inserted dynamically (via `ViewContainerRef`)\n *  - Those that have a structural directive (which, under the hood, are a subset of the above ones)\n *\n * <div class=\"alert is-helpful\">\n *\n *  Note that elements will be successfully queried via `:enter`/`:leave` even if their\n *  insertion/removal is not done manually via `ViewContainerRef`or caused by their structural\n *  directive (e.g. they enter/exit alongside their parent).\n *\n * </div>\n *\n * <div class=\"alert is-important\">\n *\n *  There is an exception to what previously mentioned, besides elements entering/leaving based on\n *  their own logic, elements with an animation trigger can always be queried via `:leave` when\n * their parent is also leaving.\n *\n * </div>\n *\n * ### Usage Example\n *\n * The following example queries for inner elements and animates them\n * individually using `animate()`.\n *\n * ```typescript\n * @Component({\n *   selector: 'inner',\n *   template: `\n *     <div [@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 }))),\n *        query('.content', animate(1000, style({ opacity: 1 }))),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction query(selector, animation, options = null) {\n    return { type: AnimationMetadataType.Query, selector, animation, options };\n}\n/**\n * Use within an animation `query()` call to issue a timing gap after\n * each queried item is animated.\n *\n * @param timings A delay value.\n * @param animation One ore more animation steps.\n * @returns An object that encapsulates the stagger data.\n *\n * @usageNotes\n * In the following example, a container element wraps a list of items stamped out\n * by an `ngFor`. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * Each time items are added, the opacity fade-in animation runs,\n * and each removed item is faded out.\n * When either of these animations occur, the stagger effect is\n * applied after each item's animation is started.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [@listAnimation]=\"items.length\">\n *   <div *ngFor=\"let item of items\">\n *     {{ item }}\n *   </div>\n * </div>\n * ```\n *\n * Here is the component code:\n *\n * ```typescript\n * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n * @Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *     ...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *    }\n *  }\n * ```\n *\n * Here is the animation trigger code:\n *\n * ```typescript\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * @publicApi\n */\nfunction stagger(timings, animation) {\n    return { type: AnimationMetadataType.Stagger, timings, animation };\n}\n\n/**\n * An injectable service that produces an animation sequence programmatically within an\n * Angular component or directive.\n * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n *\n * @usageNotes\n *\n * To use this service, add it to your component or directive as a dependency.\n * The service is instantiated along with your component.\n *\n * Apps do not typically need to create their own animation players, but if you\n * do need to, follow these steps:\n *\n * 1. Use the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code> method\n * to create a programmatic animation. The method returns an `AnimationFactory` instance.\n *\n * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n *\n * 3. Use the player object to control the animation programmatically.\n *\n * For example:\n *\n * ```ts\n * // import the service from BrowserAnimationsModule\n * import {AnimationBuilder} from '@angular/animations';\n * // require the service as a dependency\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first define a reusable animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // use the returned factory object to create a player\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nclass AnimationBuilder {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AnimationBuilder, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AnimationBuilder, providedIn: 'root', useFactory: () => inject(BrowserAnimationBuilder) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AnimationBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: () => inject(BrowserAnimationBuilder) }]\n        }] });\n/**\n * A factory object returned from the\n * <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n * method.\n *\n * @publicApi\n */\nclass AnimationFactory {\n}\nclass BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n        super();\n        this.animationModuleType = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this._nextAnimationId = 0;\n        const typeData = {\n            id: '0',\n            encapsulation: ViewEncapsulation.None,\n            styles: [],\n            data: { animation: [] },\n        };\n        this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n        if (this.animationModuleType === null && !isAnimationRenderer(this._renderer)) {\n            // We only support AnimationRenderer & DynamicDelegationRenderer for this AnimationBuilder\n            throw new ɵRuntimeError(3600 /* RuntimeErrorCode.BROWSER_ANIMATION_BUILDER_INJECTED_WITHOUT_ANIMATIONS */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                'Angular detected that the `AnimationBuilder` was injected, but animation support was not enabled. ' +\n                    'Please make sure that you enable animations in your application by calling `provideAnimations()` or `provideAnimationsAsync()` function.');\n        }\n    }\n    build(animation) {\n        const id = this._nextAnimationId;\n        this._nextAnimationId++;\n        const entry = Array.isArray(animation) ? sequence(animation) : animation;\n        issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n        return new BrowserAnimationFactory(id, this._renderer);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserAnimationBuilder, deps: [{ token: i0.RendererFactory2 }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserAnimationBuilder, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: BrowserAnimationBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.RendererFactory2 }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\nclass BrowserAnimationFactory extends AnimationFactory {\n    constructor(_id, _renderer) {\n        super();\n        this._id = _id;\n        this._renderer = _renderer;\n    }\n    create(element, options) {\n        return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n    }\n}\nclass RendererAnimationPlayer {\n    constructor(id, element, options, _renderer) {\n        this.id = id;\n        this.element = element;\n        this._renderer = _renderer;\n        this.parentPlayer = null;\n        this._started = false;\n        this.totalTime = 0;\n        this._command('create', options);\n    }\n    _listen(eventName, callback) {\n        return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n    }\n    _command(command, ...args) {\n        issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n    }\n    onDone(fn) {\n        this._listen('done', fn);\n    }\n    onStart(fn) {\n        this._listen('start', fn);\n    }\n    onDestroy(fn) {\n        this._listen('destroy', fn);\n    }\n    init() {\n        this._command('init');\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        this._command('play');\n        this._started = true;\n    }\n    pause() {\n        this._command('pause');\n    }\n    restart() {\n        this._command('restart');\n    }\n    finish() {\n        this._command('finish');\n    }\n    destroy() {\n        this._command('destroy');\n    }\n    reset() {\n        this._command('reset');\n        this._started = false;\n    }\n    setPosition(p) {\n        this._command('setPosition', p);\n    }\n    getPosition() {\n        return unwrapAnimationRenderer(this._renderer)?.engine?.players[this.id]?.getPosition() ?? 0;\n    }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n    renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n/**\n * The following 2 methods cannot reference their correct types (AnimationRenderer &\n * DynamicDelegationRenderer) since this would introduce a import cycle.\n */\nfunction unwrapAnimationRenderer(renderer) {\n    const type = renderer.ɵtype;\n    if (type === 0 /* AnimationRendererType.Regular */) {\n        return renderer;\n    }\n    else if (type === 1 /* AnimationRendererType.Delegated */) {\n        return renderer.animationRenderer;\n    }\n    return null;\n}\nfunction isAnimationRenderer(renderer) {\n    const type = renderer.ɵtype;\n    return type === 0 /* AnimationRendererType.Regular */ || type === 1 /* AnimationRendererType.Delegated */;\n}\n\n/**\n * An empty programmatic controller for reusable animations.\n * Used internally when animations are disabled, to avoid\n * checking for the null case when an animation player is expected.\n *\n * @see {@link animate}\n * @see {@link AnimationPlayer}\n * @see {@link ɵAnimationGroupPlayer AnimationGroupPlayer}\n *\n * @publicApi\n */\nclass NoopAnimationPlayer {\n    constructor(duration = 0, delay = 0) {\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._onDestroyFns = [];\n        this._originalOnDoneFns = [];\n        this._originalOnStartFns = [];\n        this._started = false;\n        this._destroyed = false;\n        this._finished = false;\n        this._position = 0;\n        this.parentPlayer = null;\n        this.totalTime = duration + delay;\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    onStart(fn) {\n        this._originalOnStartFns.push(fn);\n        this._onStartFns.push(fn);\n    }\n    onDone(fn) {\n        this._originalOnDoneFns.push(fn);\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    hasStarted() {\n        return this._started;\n    }\n    init() { }\n    play() {\n        if (!this.hasStarted()) {\n            this._onStart();\n            this.triggerMicrotask();\n        }\n        this._started = true;\n    }\n    /** @internal */\n    triggerMicrotask() {\n        queueMicrotask(() => this._onFinish());\n    }\n    _onStart() {\n        this._onStartFns.forEach((fn) => fn());\n        this._onStartFns = [];\n    }\n    pause() { }\n    restart() { }\n    finish() {\n        this._onFinish();\n    }\n    destroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            if (!this.hasStarted()) {\n                this._onStart();\n            }\n            this.finish();\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    reset() {\n        this._started = false;\n        this._finished = false;\n        this._onStartFns = this._originalOnStartFns;\n        this._onDoneFns = this._originalOnDoneFns;\n    }\n    setPosition(position) {\n        this._position = this.totalTime ? position * this.totalTime : 1;\n    }\n    getPosition() {\n        return this.totalTime ? this._position / this.totalTime : 1;\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\n/**\n * A programmatic controller for a group of reusable animations.\n * Used internally to control animations.\n *\n * @see {@link AnimationPlayer}\n * @see {@link animations/group group}\n *\n */\nclass AnimationGroupPlayer {\n    constructor(_players) {\n        this._onDoneFns = [];\n        this._onStartFns = [];\n        this._finished = false;\n        this._started = false;\n        this._destroyed = false;\n        this._onDestroyFns = [];\n        this.parentPlayer = null;\n        this.totalTime = 0;\n        this.players = _players;\n        let doneCount = 0;\n        let destroyCount = 0;\n        let startCount = 0;\n        const total = this.players.length;\n        if (total == 0) {\n            queueMicrotask(() => this._onFinish());\n        }\n        else {\n            this.players.forEach((player) => {\n                player.onDone(() => {\n                    if (++doneCount == total) {\n                        this._onFinish();\n                    }\n                });\n                player.onDestroy(() => {\n                    if (++destroyCount == total) {\n                        this._onDestroy();\n                    }\n                });\n                player.onStart(() => {\n                    if (++startCount == total) {\n                        this._onStart();\n                    }\n                });\n            });\n        }\n        this.totalTime = this.players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    init() {\n        this.players.forEach((player) => player.init());\n    }\n    onStart(fn) {\n        this._onStartFns.push(fn);\n    }\n    _onStart() {\n        if (!this.hasStarted()) {\n            this._started = true;\n            this._onStartFns.forEach((fn) => fn());\n            this._onStartFns = [];\n        }\n    }\n    onDone(fn) {\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        if (!this.parentPlayer) {\n            this.init();\n        }\n        this._onStart();\n        this.players.forEach((player) => player.play());\n    }\n    pause() {\n        this.players.forEach((player) => player.pause());\n    }\n    restart() {\n        this.players.forEach((player) => player.restart());\n    }\n    finish() {\n        this._onFinish();\n        this.players.forEach((player) => player.finish());\n    }\n    destroy() {\n        this._onDestroy();\n    }\n    _onDestroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._onFinish();\n            this.players.forEach((player) => player.destroy());\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    reset() {\n        this.players.forEach((player) => player.reset());\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    }\n    setPosition(p) {\n        const timeAtPosition = p * this.totalTime;\n        this.players.forEach((player) => {\n            const position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n            player.setPosition(position);\n        });\n    }\n    getPosition() {\n        const longestPlayer = this.players.reduce((longestSoFar, player) => {\n            const newPlayerIsLongest = longestSoFar === null || player.totalTime > longestSoFar.totalTime;\n            return newPlayerIsLongest ? player : longestSoFar;\n        }, null);\n        return longestPlayer != null ? longestPlayer.getPosition() : 0;\n    }\n    beforeDestroy() {\n        this.players.forEach((player) => {\n            if (player.beforeDestroy) {\n                player.beforeDestroy();\n            }\n        });\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\nconst ɵPRE_STYLE = '!';\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTO_STYLE, AnimationBuilder, AnimationFactory, AnimationMetadataType, NoopAnimationPlayer, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, AnimationGroupPlayer as ɵAnimationGroupPlayer, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, ɵPRE_STYLE };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,MAAM,QAAQ,eAAe;;AAEnH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9B;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC7E;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC3E;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC3E;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACjF;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC9E;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACpE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS;AAC5E,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,IAAI,EAAEC,WAAW,EAAE;EAChC,OAAO;IAAEC,IAAI,EAAEL,qBAAqB,CAACM,OAAO;IAAEH,IAAI;IAAEC,WAAW;IAAEG,OAAO,EAAE,CAAC;EAAE,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,OAAO,EAAEC,MAAM,GAAG,IAAI,EAAE;EACrC,OAAO;IAAEL,IAAI,EAAEL,qBAAqB,CAACW,OAAO;IAAED,MAAM;IAAED;EAAQ,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACC,KAAK,EAAEN,OAAO,GAAG,IAAI,EAAE;EAClC,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACc,KAAK;IAAED,KAAK;IAAEN;EAAQ,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,QAAQA,CAACF,KAAK,EAAEN,OAAO,GAAG,IAAI,EAAE;EACrC,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACgB,QAAQ;IAAEH,KAAK;IAAEN;EAAQ,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,KAAKA,CAACC,MAAM,EAAE;EACnB,OAAO;IAAEb,IAAI,EAAEL,qBAAqB,CAACmB,KAAK;IAAET,MAAM,EAAEQ,MAAM;IAAEE,MAAM,EAAE;EAAK,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAAClB,IAAI,EAAEO,MAAM,EAAEH,OAAO,EAAE;EAClC,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACsB,KAAK;IAAEnB,IAAI;IAAEO,MAAM;IAAEH;EAAQ,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,SAASA,CAACV,KAAK,EAAE;EACtB,OAAO;IAAER,IAAI,EAAEL,qBAAqB,CAACwB,SAAS;IAAEX;EAAM,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,UAAUA,CAACC,eAAe,EAAEb,KAAK,EAAEN,OAAO,GAAG,IAAI,EAAE;EACxD,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAAC2B,UAAU;IAAEC,IAAI,EAAEF,eAAe;IAAEG,SAAS,EAAEhB,KAAK;IAAEN;EAAQ,CAAC;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsB,SAASA,CAAChB,KAAK,EAAEN,OAAO,GAAG,IAAI,EAAE;EACtC,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAAC8B,SAAS;IAAED,SAAS,EAAEhB,KAAK;IAAEN;EAAQ,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,YAAYA,CAACxB,OAAO,GAAG,IAAI,EAAE;EAClC,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACgC,YAAY;IAAEzB;EAAQ,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0B,YAAYA,CAACJ,SAAS,EAAEtB,OAAO,GAAG,IAAI,EAAE;EAC7C,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACkC,UAAU;IAAEL,SAAS;IAAEtB;EAAQ,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,KAAKA,CAACC,QAAQ,EAAEP,SAAS,EAAEtB,OAAO,GAAG,IAAI,EAAE;EAChD,OAAO;IAAEF,IAAI,EAAEL,qBAAqB,CAACqC,KAAK;IAAED,QAAQ;IAAEP,SAAS;IAAEtB;EAAQ,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+B,OAAOA,CAAC7B,OAAO,EAAEoB,SAAS,EAAE;EACjC,OAAO;IAAExB,IAAI,EAAEL,qBAAqB,CAACuC,OAAO;IAAE9B,OAAO;IAAEoB;EAAU,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFH,gBAAgB;IAAA,CAAoD;EAAE;EACjL;IAAS,IAAI,CAACI,KAAK,kBAD8EnD,EAAE,CAAAoD,kBAAA;MAAAC,KAAA,EACYN,gBAAgB;MAAAO,OAAA,EAAAA,CAAA,MAAkC,MAAMrD,MAAM,CAACsD,uBAAuB,CAAC;MAAAC,UAAA,EAAzD;IAAM,EAAsD;EAAE;AAC/M;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGzD,EAAE,CAAA0D,iBAAA,CAGXX,gBAAgB,EAAc,CAAC;IAC/GnC,IAAI,EAAEV,UAAU;IAChByD,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE,MAAM;MAAEI,UAAU,EAAEA,CAAA,KAAM3D,MAAM,CAACsD,uBAAuB;IAAE,CAAC;EACpF,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,gBAAgB,CAAC;AAEvB,MAAMN,uBAAuB,SAASR,gBAAgB,CAAC;EACnDe,WAAWA,CAACC,YAAY,EAAEC,GAAG,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,mBAAmB,GAAGhE,MAAM,CAACE,qBAAqB,EAAE;MAAE+D,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC5E,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,MAAMC,QAAQ,GAAG;MACbC,EAAE,EAAE,GAAG;MACPC,aAAa,EAAElE,iBAAiB,CAACmE,IAAI;MACrCtD,MAAM,EAAE,EAAE;MACVuD,IAAI,EAAE;QAAEpC,SAAS,EAAE;MAAG;IAC1B,CAAC;IACD,IAAI,CAACqC,SAAS,GAAGV,YAAY,CAACW,cAAc,CAACV,GAAG,CAACW,IAAI,EAAEP,QAAQ,CAAC;IAChE,IAAI,IAAI,CAACH,mBAAmB,KAAK,IAAI,IAAI,CAACW,mBAAmB,CAAC,IAAI,CAACH,SAAS,CAAC,EAAE;MAC3E;MACA,MAAM,IAAIpE,aAAa,CAAC,IAAI,CAAC,8EAA8E,CAAC,OAAOoD,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrJ,oGAAoG,GAChG,0IAA0I,CAAC;IACvJ;EACJ;EACAoB,KAAKA,CAACzC,SAAS,EAAE;IACb,MAAMiC,EAAE,GAAG,IAAI,CAACF,gBAAgB;IAChC,IAAI,CAACA,gBAAgB,EAAE;IACvB,MAAMW,KAAK,GAAGC,KAAK,CAACC,OAAO,CAAC5C,SAAS,CAAC,GAAGd,QAAQ,CAACc,SAAS,CAAC,GAAGA,SAAS;IACxE6C,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,EAAEJ,EAAE,EAAE,UAAU,EAAE,CAACS,KAAK,CAAC,CAAC;IACpE,OAAO,IAAII,uBAAuB,CAACb,EAAE,EAAE,IAAI,CAACI,SAAS,CAAC;EAC1D;EACA;IAAS,IAAI,CAACzB,IAAI,YAAAmC,gCAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAyFK,uBAAuB,EA1CjCvD,EAAE,CAAAoF,QAAA,CA0CiDpF,EAAE,CAACqF,gBAAgB,GA1CtErF,EAAE,CAAAoF,QAAA,CA0CiFrF,QAAQ;IAAA,CAA6C;EAAE;EAC3O;IAAS,IAAI,CAACoD,KAAK,kBA3C8EnD,EAAE,CAAAoD,kBAAA;MAAAC,KAAA,EA2CYE,uBAAuB;MAAAD,OAAA,EAAvBC,uBAAuB,CAAAP,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7CqGzD,EAAE,CAAA0D,iBAAA,CA6CXH,uBAAuB,EAAc,CAAC;IACtH3C,IAAI,EAAEV,UAAU;IAChByD,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5C,IAAI,EAAEZ,EAAE,CAACqF;EAAiB,CAAC,EAAE;IAAEzE,IAAI,EAAE0E,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC7E3E,IAAI,EAAEN,MAAM;MACZqD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAMmF,uBAAuB,SAASrB,gBAAgB,CAAC;EACnDC,WAAWA,CAAC0B,GAAG,EAAEf,SAAS,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACe,GAAG,GAAGA,GAAG;IACd,IAAI,CAACf,SAAS,GAAGA,SAAS;EAC9B;EACAgB,MAAMA,CAACC,OAAO,EAAE5E,OAAO,EAAE;IACrB,OAAO,IAAI6E,uBAAuB,CAAC,IAAI,CAACH,GAAG,EAAEE,OAAO,EAAE5E,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC2D,SAAS,CAAC;EACxF;AACJ;AACA,MAAMkB,uBAAuB,CAAC;EAC1B7B,WAAWA,CAACO,EAAE,EAAEqB,OAAO,EAAE5E,OAAO,EAAE2D,SAAS,EAAE;IACzC,IAAI,CAACJ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACqB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACmB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAEjF,OAAO,CAAC;EACpC;EACAkF,OAAOA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACzB,OAAO,IAAI,CAACzB,SAAS,CAAC0B,MAAM,CAAC,IAAI,CAACT,OAAO,EAAE,KAAK,IAAI,CAACrB,EAAE,IAAI4B,SAAS,EAAE,EAAEC,QAAQ,CAAC;EACrF;EACAH,QAAQA,CAACK,OAAO,EAAE,GAAGzC,IAAI,EAAE;IACvBsB,qBAAqB,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,CAACiB,OAAO,EAAE,IAAI,CAACrB,EAAE,EAAE+B,OAAO,EAAEzC,IAAI,CAAC;EAC/E;EACA0C,MAAMA,CAACC,EAAE,EAAE;IACP,IAAI,CAACN,OAAO,CAAC,MAAM,EAAEM,EAAE,CAAC;EAC5B;EACAC,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,CAACN,OAAO,CAAC,OAAO,EAAEM,EAAE,CAAC;EAC7B;EACAE,SAASA,CAACF,EAAE,EAAE;IACV,IAAI,CAACN,OAAO,CAAC,SAAS,EAAEM,EAAE,CAAC;EAC/B;EACAG,IAAIA,CAAA,EAAG;IACH,IAAI,CAACV,QAAQ,CAAC,MAAM,CAAC;EACzB;EACAW,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAc,IAAIA,CAAA,EAAG;IACH,IAAI,CAACZ,QAAQ,CAAC,MAAM,CAAC;IACrB,IAAI,CAACF,QAAQ,GAAG,IAAI;EACxB;EACAe,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACb,QAAQ,CAAC,OAAO,CAAC;EAC1B;EACAc,OAAOA,CAAA,EAAG;IACN,IAAI,CAACd,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAe,MAAMA,CAAA,EAAG;IACL,IAAI,CAACf,QAAQ,CAAC,QAAQ,CAAC;EAC3B;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChB,QAAQ,CAAC,SAAS,CAAC;EAC5B;EACAiB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,QAAQ,CAAC,OAAO,CAAC;IACtB,IAAI,CAACF,QAAQ,GAAG,KAAK;EACzB;EACAoB,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI,CAACnB,QAAQ,CAAC,aAAa,EAAEmB,CAAC,CAAC;EACnC;EACAC,WAAWA,CAAA,EAAG;IACV,OAAOC,uBAAuB,CAAC,IAAI,CAAC3C,SAAS,CAAC,EAAE4C,MAAM,EAAEC,OAAO,CAAC,IAAI,CAACjD,EAAE,CAAC,EAAE8C,WAAW,CAAC,CAAC,IAAI,CAAC;EAChG;AACJ;AACA,SAASlC,qBAAqBA,CAACsC,QAAQ,EAAE7B,OAAO,EAAErB,EAAE,EAAE+B,OAAO,EAAEzC,IAAI,EAAE;EACjE4D,QAAQ,CAACC,WAAW,CAAC9B,OAAO,EAAE,KAAKrB,EAAE,IAAI+B,OAAO,EAAE,EAAEzC,IAAI,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASyD,uBAAuBA,CAACG,QAAQ,EAAE;EACvC,MAAM3G,IAAI,GAAG2G,QAAQ,CAACE,KAAK;EAC3B,IAAI7G,IAAI,KAAK,CAAC,CAAC,qCAAqC;IAChD,OAAO2G,QAAQ;EACnB,CAAC,MACI,IAAI3G,IAAI,KAAK,CAAC,CAAC,uCAAuC;IACvD,OAAO2G,QAAQ,CAACG,iBAAiB;EACrC;EACA,OAAO,IAAI;AACf;AACA,SAAS9C,mBAAmBA,CAAC2C,QAAQ,EAAE;EACnC,MAAM3G,IAAI,GAAG2G,QAAQ,CAACE,KAAK;EAC3B,OAAO7G,IAAI,KAAK,CAAC,CAAC,uCAAuCA,IAAI,KAAK,CAAC,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+G,mBAAmB,CAAC;EACtB7D,WAAWA,CAAC8D,QAAQ,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAE;IACjC,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACrC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACzC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,SAAS,GAAG8B,QAAQ,GAAGC,KAAK;EACrC;EACAS,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACN,UAAU,CAACS,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACrC,IAAI,CAACwB,UAAU,GAAG,EAAE;IACxB;EACJ;EACAvB,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,CAAC4B,mBAAmB,CAACM,IAAI,CAAClC,EAAE,CAAC;IACjC,IAAI,CAACyB,WAAW,CAACS,IAAI,CAAClC,EAAE,CAAC;EAC7B;EACAD,MAAMA,CAACC,EAAE,EAAE;IACP,IAAI,CAAC2B,kBAAkB,CAACO,IAAI,CAAClC,EAAE,CAAC;IAChC,IAAI,CAACwB,UAAU,CAACU,IAAI,CAAClC,EAAE,CAAC;EAC5B;EACAE,SAASA,CAACF,EAAE,EAAE;IACV,IAAI,CAAC0B,aAAa,CAACQ,IAAI,CAAClC,EAAE,CAAC;EAC/B;EACAI,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAY,IAAIA,CAAA,EAAG,CAAE;EACTE,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACD,UAAU,CAAC,CAAC,EAAE;MACpB,IAAI,CAAC+B,QAAQ,CAAC,CAAC;MACf,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC7C,QAAQ,GAAG,IAAI;EACxB;EACA;EACA6C,gBAAgBA,CAAA,EAAG;IACfC,cAAc,CAAC,MAAM,IAAI,CAACL,SAAS,CAAC,CAAC,CAAC;EAC1C;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACV,WAAW,CAACQ,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IACtC,IAAI,CAACyB,WAAW,GAAG,EAAE;EACzB;EACAnB,KAAKA,CAAA,EAAG,CAAE;EACVC,OAAOA,CAAA,EAAG,CAAE;EACZC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACwB,SAAS,CAAC,CAAC;EACpB;EACAvB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACoB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC,IAAI,CAACzB,UAAU,CAAC,CAAC,EAAE;QACpB,IAAI,CAAC+B,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAAC3B,MAAM,CAAC,CAAC;MACb,IAAI,CAACkB,aAAa,CAACO,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACxC,IAAI,CAAC0B,aAAa,GAAG,EAAE;IAC3B;EACJ;EACAhB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACnB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACuC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACL,WAAW,GAAG,IAAI,CAACG,mBAAmB;IAC3C,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACG,kBAAkB;EAC7C;EACAhB,WAAWA,CAAC2B,QAAQ,EAAE;IAClB,IAAI,CAACP,SAAS,GAAG,IAAI,CAACvC,SAAS,GAAG8C,QAAQ,GAAG,IAAI,CAAC9C,SAAS,GAAG,CAAC;EACnE;EACAqB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACuC,SAAS,GAAG,IAAI,CAACvC,SAAS,GAAG,CAAC;EAC/D;EACA;EACA+C,eAAeA,CAACC,SAAS,EAAE;IACvB,MAAMC,OAAO,GAAGD,SAAS,IAAI,OAAO,GAAG,IAAI,CAACf,WAAW,GAAG,IAAI,CAACD,UAAU;IACzEiB,OAAO,CAACR,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IAC7ByC,OAAO,CAACC,MAAM,GAAG,CAAC;EACtB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBnF,WAAWA,CAACoF,QAAQ,EAAE;IAClB,IAAI,CAACpB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACvC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACpC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,SAAS,GAAG,CAAC;IAClB,IAAI,CAACwB,OAAO,GAAG4B,QAAQ;IACvB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAMC,KAAK,GAAG,IAAI,CAAChC,OAAO,CAAC0B,MAAM;IACjC,IAAIM,KAAK,IAAI,CAAC,EAAE;MACZX,cAAc,CAAC,MAAM,IAAI,CAACL,SAAS,CAAC,CAAC,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAAChB,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAK;QAC7BA,MAAM,CAAClD,MAAM,CAAC,MAAM;UAChB,IAAI,EAAE8C,SAAS,IAAIG,KAAK,EAAE;YACtB,IAAI,CAAChB,SAAS,CAAC,CAAC;UACpB;QACJ,CAAC,CAAC;QACFiB,MAAM,CAAC/C,SAAS,CAAC,MAAM;UACnB,IAAI,EAAE4C,YAAY,IAAIE,KAAK,EAAE;YACzB,IAAI,CAACE,UAAU,CAAC,CAAC;UACrB;QACJ,CAAC,CAAC;QACFD,MAAM,CAAChD,OAAO,CAAC,MAAM;UACjB,IAAI,EAAE8C,UAAU,IAAIC,KAAK,EAAE;YACvB,IAAI,CAACb,QAAQ,CAAC,CAAC;UACnB;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA,IAAI,CAAC3C,SAAS,GAAG,IAAI,CAACwB,OAAO,CAACmC,MAAM,CAAC,CAACC,IAAI,EAAEH,MAAM,KAAKI,IAAI,CAACC,GAAG,CAACF,IAAI,EAAEH,MAAM,CAACzD,SAAS,CAAC,EAAE,CAAC,CAAC;EAC/F;EACAwC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACN,UAAU,CAACS,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACrC,IAAI,CAACwB,UAAU,GAAG,EAAE;IACxB;EACJ;EACArB,IAAIA,CAAA,EAAG;IACH,IAAI,CAACa,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAAC9C,IAAI,CAAC,CAAC,CAAC;EACnD;EACAF,OAAOA,CAACD,EAAE,EAAE;IACR,IAAI,CAACyB,WAAW,CAACS,IAAI,CAAClC,EAAE,CAAC;EAC7B;EACAmC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC/B,UAAU,CAAC,CAAC,EAAE;MACpB,IAAI,CAACb,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACkC,WAAW,CAACQ,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACtC,IAAI,CAACyB,WAAW,GAAG,EAAE;IACzB;EACJ;EACA1B,MAAMA,CAACC,EAAE,EAAE;IACP,IAAI,CAACwB,UAAU,CAACU,IAAI,CAAClC,EAAE,CAAC;EAC5B;EACAE,SAASA,CAACF,EAAE,EAAE;IACV,IAAI,CAAC0B,aAAa,CAACQ,IAAI,CAAClC,EAAE,CAAC;EAC/B;EACAI,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ;EACxB;EACAc,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACf,YAAY,EAAE;MACpB,IAAI,CAACa,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACgC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACnB,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAAC5C,IAAI,CAAC,CAAC,CAAC;EACnD;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACU,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAAC3C,KAAK,CAAC,CAAC,CAAC;EACpD;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACS,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAAC1C,OAAO,CAAC,CAAC,CAAC;EACtD;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACwB,SAAS,CAAC,CAAC;IAChB,IAAI,CAAChB,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAACzC,MAAM,CAAC,CAAC,CAAC;EACrD;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyC,UAAU,CAAC,CAAC;EACrB;EACAA,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACrB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACG,SAAS,CAAC,CAAC;MAChB,IAAI,CAAChB,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAACxC,OAAO,CAAC,CAAC,CAAC;MAClD,IAAI,CAACiB,aAAa,CAACO,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACxC,IAAI,CAAC0B,aAAa,GAAG,EAAE;IAC3B;EACJ;EACAhB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACM,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAKA,MAAM,CAACvC,KAAK,CAAC,CAAC,CAAC;IAChD,IAAI,CAACmB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACvC,QAAQ,GAAG,KAAK;EACzB;EACAoB,WAAWA,CAACC,CAAC,EAAE;IACX,MAAM2C,cAAc,GAAG3C,CAAC,GAAG,IAAI,CAACpB,SAAS;IACzC,IAAI,CAACwB,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAK;MAC7B,MAAMX,QAAQ,GAAGW,MAAM,CAACzD,SAAS,GAAG6D,IAAI,CAACG,GAAG,CAAC,CAAC,EAAED,cAAc,GAAGN,MAAM,CAACzD,SAAS,CAAC,GAAG,CAAC;MACtFyD,MAAM,CAACtC,WAAW,CAAC2B,QAAQ,CAAC;IAChC,CAAC,CAAC;EACN;EACAzB,WAAWA,CAAA,EAAG;IACV,MAAM4C,aAAa,GAAG,IAAI,CAACzC,OAAO,CAACmC,MAAM,CAAC,CAACO,YAAY,EAAET,MAAM,KAAK;MAChE,MAAMU,kBAAkB,GAAGD,YAAY,KAAK,IAAI,IAAIT,MAAM,CAACzD,SAAS,GAAGkE,YAAY,CAAClE,SAAS;MAC7F,OAAOmE,kBAAkB,GAAGV,MAAM,GAAGS,YAAY;IACrD,CAAC,EAAE,IAAI,CAAC;IACR,OAAOD,aAAa,IAAI,IAAI,GAAGA,aAAa,CAAC5C,WAAW,CAAC,CAAC,GAAG,CAAC;EAClE;EACA+C,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC5C,OAAO,CAACiB,OAAO,CAAEgB,MAAM,IAAK;MAC7B,IAAIA,MAAM,CAACW,aAAa,EAAE;QACtBX,MAAM,CAACW,aAAa,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACA;EACArB,eAAeA,CAACC,SAAS,EAAE;IACvB,MAAMC,OAAO,GAAGD,SAAS,IAAI,OAAO,GAAG,IAAI,CAACf,WAAW,GAAG,IAAI,CAACD,UAAU;IACzEiB,OAAO,CAACR,OAAO,CAAEjC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;IAC7ByC,OAAO,CAACC,MAAM,GAAG,CAAC;EACtB;AACJ;AAEA,MAAMmB,UAAU,GAAG,GAAG;;AAEtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS3J,UAAU,EAAEuC,gBAAgB,EAAEc,gBAAgB,EAAEtD,qBAAqB,EAAEoH,mBAAmB,EAAE5G,OAAO,EAAEuB,YAAY,EAAEF,SAAS,EAAEjB,KAAK,EAAEW,SAAS,EAAEY,KAAK,EAAEpB,QAAQ,EAAEuB,OAAO,EAAEjB,KAAK,EAAEJ,KAAK,EAAEQ,UAAU,EAAEvB,OAAO,EAAE+B,YAAY,EAAEyG,oBAAoB,IAAImB,qBAAqB,EAAE7G,uBAAuB,IAAI8G,wBAAwB,EAAEF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}