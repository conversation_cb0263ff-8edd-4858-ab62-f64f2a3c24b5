/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* Custom Bootstrap overrides */
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* Sidebar styles */
.sidebar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  border-radius: 8px;
  margin: 4px 0;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

/* Card styles */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Table styles */
.table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.table thead th {
  background-color: #f8f9fa;
  border: none;
  font-weight: 600;
  color: #495057;
}

/* Form styles */
.form-control {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert styles */
.alert {
  border-radius: 8px;
  border: none;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Status badges */
.badge {
  font-size: 0.75em;
  padding: 0.5em 0.75em;
  border-radius: 6px;
}

.badge.status-active {
  background-color: #28a745;
}

.badge.status-inactive {
  background-color: #6c757d;
}

.badge.status-pending {
  background-color: #ffc107;
  color: #212529;
}

.badge.status-processing {
  background-color: #17a2b8;
}

.badge.status-shipped {
  background-color: #007bff;
}

.badge.status-delivered {
  background-color: #28a745;
}

.badge.status-cancelled {
  background-color: #dc3545;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .sidebar.show {
    left: 0;
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}
