{"name": "ecommerce-inventory-backend", "version": "1.0.0", "description": "Ecommerce Inventory Management System Backend", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecommerce", "inventory", "management", "nodejs", "typescript"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "pg": "^8.16.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.1.0", "@types/pg": "^8.15.5", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}