{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"app-sidebar\", 5);\n    i0.ɵɵelementStart(2, \"div\", 6);\n    i0.ɵɵelement(3, \"app-header\");\n    i0.ɵɵelementStart(4, \"div\", 7);\n    i0.ɵɵelement(5, \"router-outlet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'Ecommerce Inventory Management';\n    this.isAuthenticated = false;\n  }\n  ngOnInit() {\n    this.authService.isAuthenticated$.subscribe(isAuth => {\n      this.isAuthenticated = isAuth;\n      if (!isAuth && !this.isAuthRoute()) {\n        this.router.navigate(['/login']);\n      }\n    });\n  }\n  isAuthRoute() {\n    const currentRoute = this.router.url;\n    return currentRoute === '/login' || currentRoute === '/register';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"app-container\"], [\"class\", \"auth-container\", 4, \"ngIf\"], [\"class\", \"main-layout d-flex\", 4, \"ngIf\"], [1, \"auth-container\"], [1, \"main-layout\", \"d-flex\"], [1, \"sidebar-container\"], [1, \"main-content\", \"flex-grow-1\"], [1, \"content-area\", \"p-4\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_div_1_Template, 2, 0, \"div\", 1)(2, AppComponent_div_2_Template, 6, 0, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        }\n      },\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.auth-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.main-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.sidebar-container[_ngcontent-%COMP%] {\\n  width: 250px;\\n  flex-shrink: 0;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.content-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background-color: #f8f9fa;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidebar-container[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    left: -250px;\\n    height: 100vh;\\n    z-index: 1000;\\n    transition: left 0.3s ease;\\n  }\\n  .sidebar-container.show[_ngcontent-%COMP%] {\\n    left: 0;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "AppComponent", "constructor", "authService", "router", "title", "isAuthenticated", "ngOnInit", "isAuthenticated$", "subscribe", "isAuth", "isAuthRoute", "navigate", "currentRoute", "url", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵtemplate", "AppComponent_div_1_Template", "AppComponent_div_2_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\app.component.ts", "D:\\Ecommerce Inventory Management\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from './services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit {\n  title = 'Ecommerce Inventory Management';\n  isAuthenticated = false;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.authService.isAuthenticated$.subscribe(\n      isAuth => {\n        this.isAuthenticated = isAuth;\n        if (!isAuth && !this.isAuthRoute()) {\n          this.router.navigate(['/login']);\n        }\n      }\n    );\n  }\n\n  private isAuthRoute(): boolean {\n    const currentRoute = this.router.url;\n    return currentRoute === '/login' || currentRoute === '/register';\n  }\n}\n", "<div class=\"app-container\">\n  <div *ngIf=\"!isAuthenticated\" class=\"auth-container\">\n    <router-outlet></router-outlet>\n  </div>\n  \n  <div *ngIf=\"isAuthenticated\" class=\"main-layout d-flex\">\n    <app-sidebar class=\"sidebar-container\"></app-sidebar>\n    <div class=\"main-content flex-grow-1\">\n      <app-header></app-header>\n      <div class=\"content-area p-4\">\n        <router-outlet></router-outlet>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;ICCEA,EAAA,CAAAC,cAAA,aAAqD;IACnDD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,qBAAqD;IACrDF,EAAA,CAAAC,cAAA,aAAsC;IACpCD,EAAA,CAAAE,SAAA,iBAAyB;IACzBF,EAAA,CAAAC,cAAA,aAA8B;IAC5BD,EAAA,CAAAE,SAAA,oBAA+B;IAGrCF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;ADJR,OAAM,MAAOC,YAAY;EAIvBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,KAAK,GAAG,gCAAgC;IACxC,KAAAC,eAAe,GAAG,KAAK;EAKpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,gBAAgB,CAACC,SAAS,CACzCC,MAAM,IAAG;MACP,IAAI,CAACJ,eAAe,GAAGI,MAAM;MAC7B,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,EAAE;QAClC,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAEpC,CAAC,CACF;EACH;EAEQD,WAAWA,CAAA;IACjB,MAAME,YAAY,GAAG,IAAI,CAACT,MAAM,CAACU,GAAG;IACpC,OAAOD,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,WAAW;EAClE;;;uBAvBWZ,YAAY,EAAAJ,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApB,EAAA,CAAAkB,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZlB,YAAY;MAAAmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzB7B,EAAA,CAAAC,cAAA,aAA2B;UAKzBD,EAJA,CAAA+B,UAAA,IAAAC,2BAAA,iBAAqD,IAAAC,2BAAA,iBAIG;UAS1DjC,EAAA,CAAAG,YAAA,EAAM;;;UAbEH,EAAA,CAAAkC,SAAA,EAAsB;UAAtBlC,EAAA,CAAAmC,UAAA,UAAAL,GAAA,CAAArB,eAAA,CAAsB;UAItBT,EAAA,CAAAkC,SAAA,EAAqB;UAArBlC,EAAA,CAAAmC,UAAA,SAAAL,GAAA,CAAArB,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}