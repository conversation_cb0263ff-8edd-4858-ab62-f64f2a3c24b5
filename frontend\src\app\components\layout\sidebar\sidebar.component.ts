import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { User } from '../../../models/user.model';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit {
  currentUser: User | null = null;

  menuItems = [
    {
      label: 'Dashboard',
      icon: 'fas fa-tachometer-alt',
      route: '/dashboard',
      roles: ['admin', 'manager', 'employee']
    },
    {
      label: 'Products',
      icon: 'fas fa-box',
      route: '/products',
      roles: ['admin', 'manager', 'employee']
    },
    {
      label: 'Inventory',
      icon: 'fas fa-warehouse',
      route: '/inventory',
      roles: ['admin', 'manager', 'employee']
    },
    {
      label: 'Customers',
      icon: 'fas fa-users',
      route: '/customers',
      roles: ['admin', 'manager', 'employee']
    },
    {
      label: 'Orders',
      icon: 'fas fa-shopping-cart',
      route: '/orders',
      roles: ['admin', 'manager', 'employee']
    },
    {
      label: 'Analytics',
      icon: 'fas fa-chart-bar',
      route: '/analytics',
      roles: ['admin', 'manager']
    }
  ];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  isActiveRoute(route: string): boolean {
    return this.router.url.startsWith(route);
  }

  hasAccess(roles: string[]): boolean {
    return this.authService.hasRole(roles);
  }
}
