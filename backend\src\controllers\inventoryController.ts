import { Request, Response } from 'express';
import pool from '../config/database';
import { AuthRequest } from '../types';

export const getAllInventory = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, low_stock = false } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    let query = `
      SELECT i.*, p.name as product_name, p.sku, p.price, c.name as category_name
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `;
    const queryParams: any[] = [];

    if (low_stock === 'true') {
      query += ` AND i.quantity <= i.min_stock_level`;
    }

    query += ` ORDER BY i.last_updated DESC LIMIT $1 OFFSET $2`;
    queryParams.push(Number(limit), offset);

    const result = await pool.query(query, queryParams);

    // Get total count
    let countQuery = `
      SELECT COUNT(*) FROM inventory i
      JOIN products p ON i.product_id = p.id
      WHERE 1=1
    `;
    if (low_stock === 'true') {
      countQuery += ` AND i.quantity <= i.min_stock_level`;
    }

    const countResult = await pool.query(countQuery);
    const totalItems = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalItems / Number(limit));

    res.json({
      inventory: result.rows,
      pagination: {
        currentPage: Number(page),
        totalPages,
        totalItems,
        itemsPerPage: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get inventory error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getInventoryByProduct = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    const result = await pool.query(`
      SELECT i.*, p.name as product_name, p.sku, p.price
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      WHERE i.product_id = $1
    `, [productId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Inventory record not found for this product' });
    }

    res.json({ inventory: result.rows[0] });
  } catch (error) {
    console.error('Get inventory by product error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const updateInventory = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { quantity, min_stock_level, max_stock_level, location } = req.body;

    const result = await pool.query(`
      UPDATE inventory 
      SET quantity = $1, min_stock_level = $2, max_stock_level = $3, location = $4, last_updated = NOW()
      WHERE product_id = $5
      RETURNING *
    `, [quantity, min_stock_level, max_stock_level, location, productId]);

    if (result.rows.length === 0) {
      // Create new inventory record if it doesn't exist
      const newResult = await pool.query(`
        INSERT INTO inventory (product_id, quantity, min_stock_level, max_stock_level, location, last_updated)
        VALUES ($1, $2, $3, $4, $5, NOW())
        RETURNING *
      `, [productId, quantity, min_stock_level, max_stock_level, location]);

      return res.status(201).json({
        message: 'Inventory record created successfully',
        inventory: newResult.rows[0]
      });
    }

    res.json({
      message: 'Inventory updated successfully',
      inventory: result.rows[0]
    });
  } catch (error) {
    console.error('Update inventory error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const adjustInventory = async (req: AuthRequest, res: Response) => {
  try {
    const { productId } = req.params;
    const { adjustment, notes } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Start transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Get current inventory
      const currentInventory = await client.query(
        'SELECT quantity FROM inventory WHERE product_id = $1',
        [productId]
      );

      if (currentInventory.rows.length === 0) {
        throw new Error('Inventory record not found');
      }

      const currentQuantity = currentInventory.rows[0].quantity;
      const newQuantity = Math.max(0, currentQuantity + adjustment);

      // Update inventory
      await client.query(
        'UPDATE inventory SET quantity = $1, last_updated = NOW() WHERE product_id = $2',
        [newQuantity, productId]
      );

      // Record inventory movement
      await client.query(`
        INSERT INTO inventory_movements (product_id, movement_type, quantity, reference_type, notes, created_at, created_by)
        VALUES ($1, $2, $3, 'adjustment', $4, NOW(), $5)
      `, [productId, adjustment > 0 ? 'in' : 'out', Math.abs(adjustment), notes, userId]);

      await client.query('COMMIT');

      res.json({
        message: 'Inventory adjusted successfully',
        old_quantity: currentQuantity,
        new_quantity: newQuantity,
        adjustment
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Adjust inventory error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getInventoryMovements = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const result = await pool.query(`
      SELECT im.*, p.name as product_name, p.sku, u.first_name, u.last_name
      FROM inventory_movements im
      JOIN products p ON im.product_id = p.id
      LEFT JOIN users u ON im.created_by = u.id
      WHERE im.product_id = $1
      ORDER BY im.created_at DESC
      LIMIT $2 OFFSET $3
    `, [productId, Number(limit), offset]);

    const countResult = await pool.query(
      'SELECT COUNT(*) FROM inventory_movements WHERE product_id = $1',
      [productId]
    );
    const totalItems = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalItems / Number(limit));

    res.json({
      movements: result.rows,
      pagination: {
        currentPage: Number(page),
        totalPages,
        totalItems,
        itemsPerPage: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get inventory movements error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getLowStockAlerts = async (req: Request, res: Response) => {
  try {
    const result = await pool.query(`
      SELECT i.*, p.name as product_name, p.sku, c.name as category_name
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE i.quantity <= i.min_stock_level
      ORDER BY (i.quantity::float / i.min_stock_level::float) ASC
    `);

    res.json({ alerts: result.rows });
  } catch (error) {
    console.error('Get low stock alerts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
