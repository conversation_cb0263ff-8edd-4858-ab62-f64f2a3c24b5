import { Request, Response } from 'express';
import pool from '../config/database';
import { AuthRequest } from '../types';

export const getAllOrders = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, status, customer_id } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    let query = `
      SELECT o.*, c.first_name, c.last_name, c.email
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      WHERE 1=1
    `;
    const queryParams: any[] = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      query += ` AND o.status = $${paramCount}`;
      queryParams.push(status);
    }

    if (customer_id) {
      paramCount++;
      query += ` AND o.customer_id = $${paramCount}`;
      queryParams.push(customer_id);
    }

    query += ` ORDER BY o.order_date DESC LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`;
    queryParams.push(Number(limit), offset);

    const result = await pool.query(query, queryParams);

    // Get total count
    let countQuery = 'SELECT COUNT(*) FROM orders o WHERE 1=1';
    const countParams: any[] = [];
    let countParamCount = 0;

    if (status) {
      countParamCount++;
      countQuery += ` AND o.status = $${countParamCount}`;
      countParams.push(status);
    }

    if (customer_id) {
      countParamCount++;
      countQuery += ` AND o.customer_id = $${countParamCount}`;
      countParams.push(customer_id);
    }

    const countResult = await pool.query(countQuery, countParams);
    const totalItems = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalItems / Number(limit));

    res.json({
      orders: result.rows,
      pagination: {
        currentPage: Number(page),
        totalPages,
        totalItems,
        itemsPerPage: Number(limit)
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getOrderById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const orderResult = await pool.query(`
      SELECT o.*, c.first_name, c.last_name, c.email, c.phone, c.address
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      WHERE o.id = $1
    `, [id]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const itemsResult = await pool.query(`
      SELECT oi.*, p.name as product_name, p.sku
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
    `, [id]);

    res.json({
      order: orderResult.rows[0],
      items: itemsResult.rows
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const createOrder = async (req: AuthRequest, res: Response) => {
  try {
    const { customer_id, items, shipping_address } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Start transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Generate order number
      const orderNumber = `ORD-${Date.now()}`;

      // Calculate total amount
      let totalAmount = 0;
      for (const item of items) {
        const productResult = await client.query('SELECT price FROM products WHERE id = $1', [item.product_id]);
        if (productResult.rows.length === 0) {
          throw new Error(`Product with ID ${item.product_id} not found`);
        }
        const price = productResult.rows[0].price;
        totalAmount += price * item.quantity;
      }

      // Create order
      const orderResult = await client.query(`
        INSERT INTO orders (customer_id, order_number, status, total_amount, order_date, shipping_address, created_at, updated_at)
        VALUES ($1, $2, 'pending', $3, NOW(), $4, NOW(), NOW())
        RETURNING *
      `, [customer_id, orderNumber, totalAmount, shipping_address]);

      const order = orderResult.rows[0];

      // Create order items and update inventory
      for (const item of items) {
        const productResult = await client.query('SELECT price FROM products WHERE id = $1', [item.product_id]);
        const unitPrice = productResult.rows[0].price;
        const totalPrice = unitPrice * item.quantity;

        // Insert order item
        await client.query(`
          INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
          VALUES ($1, $2, $3, $4, $5)
        `, [order.id, item.product_id, item.quantity, unitPrice, totalPrice]);

        // Update inventory
        await client.query(`
          UPDATE inventory 
          SET quantity = quantity - $1, last_updated = NOW()
          WHERE product_id = $2
        `, [item.quantity, item.product_id]);

        // Record inventory movement
        await client.query(`
          INSERT INTO inventory_movements (product_id, movement_type, quantity, reference_type, reference_id, created_at, created_by)
          VALUES ($1, 'out', $2, 'sale', $3, NOW(), $4)
        `, [item.product_id, item.quantity, order.id, userId]);
      }

      await client.query('COMMIT');

      res.status(201).json({
        message: 'Order created successfully',
        order
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const updateOrderStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const result = await pool.query(`
      UPDATE orders 
      SET status = $1, updated_at = NOW()
      WHERE id = $2
      RETURNING *
    `, [status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Order not found' });
    }

    res.json({
      message: 'Order status updated successfully',
      order: result.rows[0]
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

export const getOrderStats = async (req: Request, res: Response) => {
  try {
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
        COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(total_amount), 0) as average_order_value
      FROM orders
    `);

    const recentOrders = await pool.query(`
      SELECT o.id, o.order_number, o.status, o.total_amount, o.order_date,
             c.first_name, c.last_name
      FROM orders o
      JOIN customers c ON o.customer_id = c.id
      ORDER BY o.order_date DESC
      LIMIT 10
    `);

    res.json({
      stats: stats.rows[0],
      recent_orders: recentOrders.rows
    });
  } catch (error) {
    console.error('Get order stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
