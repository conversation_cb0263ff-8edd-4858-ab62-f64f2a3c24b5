{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeWhile(predicate, inclusive = false) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const result = predicate(value, index++);\n      (result || inclusive) && subscriber.next(value);\n      !result && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "inclusive", "source", "subscriber", "index", "subscribe", "value", "result", "next", "complete"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/operators/takeWhile.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeWhile(predicate, inclusive = false) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const result = predicate(value, index++);\n            (result || inclusive) && subscriber.next(value);\n            !result && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,SAAS,GAAG,KAAK,EAAE;EACpD,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAK;MAC7D,MAAMC,MAAM,GAAGP,SAAS,CAACM,KAAK,EAAEF,KAAK,EAAE,CAAC;MACxC,CAACG,MAAM,IAAIN,SAAS,KAAKE,UAAU,CAACK,IAAI,CAACF,KAAK,CAAC;MAC/C,CAACC,MAAM,IAAIJ,UAAU,CAACM,QAAQ,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}