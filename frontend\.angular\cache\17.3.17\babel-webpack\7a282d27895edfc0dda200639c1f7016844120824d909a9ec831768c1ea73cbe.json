{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/auth.service\";\nfunction HeaderComponent_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currentUser.first_name, \" \", ctx_r0.currentUser.last_name, \" \");\n  }\n}\nexport class HeaderComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  logout() {\n    this.authService.logout();\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      decls: 25,\n      vars: 4,\n      consts: [[1, \"header\", \"bg-white\", \"shadow-sm\", \"border-bottom\"], [1, \"container-fluid\"], [1, \"row\", \"align-items-center\", \"py-3\"], [1, \"col-md-6\"], [1, \"mb-0\", \"text-dark\"], [1, \"fas\", \"fa-boxes\", \"me-2\", \"text-primary\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-end\"], [1, \"dropdown\"], [\"type\", \"button\", \"id\", \"userDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-user-circle\", \"me-2\"], [4, \"ngIf\"], [\"aria-labelledby\", \"userDropdown\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-item-text\"], [1, \"text-muted\"], [1, \"dropdown-divider\"], [\"href\", \"#\", 1, \"dropdown-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"me-2\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h4\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵtext(6, \" Inventory Management \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"div\", 6)(9, \"div\", 7)(10, \"button\", 8);\n          i0.ɵɵelement(11, \"i\", 9);\n          i0.ɵɵtemplate(12, HeaderComponent_span_12_Template, 2, 2, \"span\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"ul\", 11)(14, \"li\")(15, \"span\", 12)(16, \"small\", 13);\n          i0.ɵɵtext(17);\n          i0.ɵɵpipe(18, \"titlecase\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"li\");\n          i0.ɵɵelement(20, \"hr\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"li\")(22, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_a_click_22_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵtext(24, \" Logout \");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"Role: \", i0.ɵɵpipeBind1(18, 2, ctx.currentUser == null ? null : ctx.currentUser.role), \"\");\n        }\n      },\n      styles: [\".header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n  background: white !important;\\n}\\n\\n.dropdown-toggle[_ngcontent-%COMP%]::after {\\n  margin-left: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL2hlYWRlci5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uL0Vjb21tZXJjZSUyMEludmVudG9yeSUyME1hbmFnZW1lbnQvZnJvbnRlbmQvc3JjL2FwcC9jb21wb25lbnRzL2xheW91dC9oZWFkZXIvaGVhZGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZ0JBQUE7RUFDQSxNQUFBO0VBQ0EsWUFBQTtFQUNBLDRCQUFBO0FDQ0Y7O0FERUE7RUFDRSxtQkFBQTtBQ0NGIiwic291cmNlc0NvbnRlbnQiOlsiLmhlYWRlciB7XG4gIHBvc2l0aW9uOiBzdGlja3k7XG4gIHRvcDogMDtcbiAgei1pbmRleDogMTAwO1xuICBiYWNrZ3JvdW5kOiB3aGl0ZSAhaW1wb3J0YW50O1xufVxuXG4uZHJvcGRvd24tdG9nZ2xlOjphZnRlciB7XG4gIG1hcmdpbi1sZWZ0OiAwLjVyZW07XG59XG4iLCIuaGVhZGVyIHtcbiAgcG9zaXRpb246IHN0aWNreTtcbiAgdG9wOiAwO1xuICB6LWluZGV4OiAxMDA7XG4gIGJhY2tncm91bmQ6IHdoaXRlICFpbXBvcnRhbnQ7XG59XG5cbi5kcm9wZG93bi10b2dnbGU6OmFmdGVyIHtcbiAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "currentUser", "first_name", "last_name", "HeaderComponent", "constructor", "authService", "ngOnInit", "currentUser$", "subscribe", "user", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "HeaderComponent_span_12_Template", "ɵɵlistener", "HeaderComponent_Template_a_click_22_listener", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "role"], "sources": ["D:\\Ecommerce Inventory Management\\frontend\\src\\app\\components\\layout\\header\\header.component.ts", "D:\\Ecommerce Inventory Management\\frontend\\src\\app\\components\\layout\\header\\header.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../services/auth.service';\nimport { User } from '../../../models/user.model';\n\n@Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent implements OnInit {\n  currentUser: User | null = null;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  logout(): void {\n    this.authService.logout();\n  }\n}\n", "<header class=\"header bg-white shadow-sm border-bottom\">\n  <div class=\"container-fluid\">\n    <div class=\"row align-items-center py-3\">\n      <div class=\"col-md-6\">\n        <h4 class=\"mb-0 text-dark\">\n          <i class=\"fas fa-boxes me-2 text-primary\"></i>\n          Inventory Management\n        </h4>\n      </div>\n      <div class=\"col-md-6\">\n        <div class=\"d-flex align-items-center justify-content-end\">\n          <div class=\"dropdown\">\n            <button class=\"btn btn-outline-secondary dropdown-toggle d-flex align-items-center\" \n                    type=\"button\" \n                    id=\"userDropdown\" \n                    data-bs-toggle=\"dropdown\" \n                    aria-expanded=\"false\">\n              <i class=\"fas fa-user-circle me-2\"></i>\n              <span *ngIf=\"currentUser\">\n                {{ currentUser.first_name }} {{ currentUser.last_name }}\n              </span>\n            </button>\n            <ul class=\"dropdown-menu dropdown-menu-end\" aria-labelledby=\"userDropdown\">\n              <li>\n                <span class=\"dropdown-item-text\">\n                  <small class=\"text-muted\">Role: {{ currentUser?.role | titlecase }}</small>\n                </span>\n              </li>\n              <li><hr class=\"dropdown-divider\"></li>\n              <li>\n                <a class=\"dropdown-item\" href=\"#\" (click)=\"logout()\">\n                  <i class=\"fas fa-sign-out-alt me-2\"></i>\n                  Logout\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</header>\n"], "mappings": ";;;;ICkBcA,EAAA,CAAAC,cAAA,WAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,UAAA,OAAAF,MAAA,CAAAC,WAAA,CAAAE,SAAA,MACF;;;ADXd,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAL,WAAW,GAAgB,IAAI;EAEgB;EAE/CM,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACT,WAAW,GAAGS,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE;EAC3B;;;uBAbWP,eAAe,EAAAV,EAAA,CAAAkB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAfV,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLpB3B,EAJR,CAAAC,cAAA,gBAAwD,aACzB,aACc,aACjB,YACO;UACzBD,EAAA,CAAA6B,SAAA,WAA8C;UAC9C7B,EAAA,CAAAE,MAAA,6BACF;UACFF,EADE,CAAAG,YAAA,EAAK,EACD;UAIAH,EAHN,CAAAC,cAAA,aAAsB,aACuC,aACnC,iBAKU;UAC5BD,EAAA,CAAA6B,SAAA,YAAuC;UACvC7B,EAAA,CAAA8B,UAAA,KAAAC,gCAAA,mBAA0B;UAG5B/B,EAAA,CAAAG,YAAA,EAAS;UAIHH,EAHN,CAAAC,cAAA,cAA2E,UACrE,gBAC+B,iBACL;UAAAD,EAAA,CAAAE,MAAA,IAAyC;;UAEvEF,EAFuE,CAAAG,YAAA,EAAQ,EACtE,EACJ;UACLH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAA6B,SAAA,cAA6B;UAAA7B,EAAA,CAAAG,YAAA,EAAK;UAEpCH,EADF,CAAAC,cAAA,UAAI,aACmD;UAAnBD,EAAA,CAAAgC,UAAA,mBAAAC,6CAAA;YAAA,OAASL,GAAA,CAAAX,MAAA,EAAQ;UAAA,EAAC;UAClDjB,EAAA,CAAA6B,SAAA,aAAwC;UACxC7B,EAAA,CAAAE,MAAA,gBACF;UAQhBF,EARgB,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF,EACF,EACF,EACC;;;UAvBYH,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAkC,UAAA,SAAAN,GAAA,CAAArB,WAAA,CAAiB;UAOMP,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAmC,kBAAA,WAAAnC,EAAA,CAAAoC,WAAA,QAAAR,GAAA,CAAArB,WAAA,kBAAAqB,GAAA,CAAArB,WAAA,CAAA8B,IAAA,MAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}