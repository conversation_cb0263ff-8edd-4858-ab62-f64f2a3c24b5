{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function audit(durationSelector) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    let durationSubscriber = null;\n    let isComplete = false;\n    const endDuration = () => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n      isComplete && subscriber.complete();\n    };\n    const cleanupDuration = () => {\n      durationSubscriber = null;\n      isComplete && subscriber.complete();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      lastValue = value;\n      if (!durationSubscriber) {\n        innerFrom(durationSelector(value)).subscribe(durationSubscriber = createOperatorSubscriber(subscriber, endDuration, cleanupDuration));\n      }\n    }, () => {\n      isComplete = true;\n      (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "innerFrom", "createOperatorSubscriber", "audit", "durationSelector", "source", "subscriber", "hasValue", "lastValue", "durationSubscriber", "isComplete", "endDuration", "unsubscribe", "value", "next", "complete", "cleanupDuration", "subscribe", "closed"], "sources": ["D:/Ecommerce Inventory Management/frontend/node_modules/rxjs/dist/esm/internal/operators/audit.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function audit(durationSelector) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        let lastValue = null;\n        let durationSubscriber = null;\n        let isComplete = false;\n        const endDuration = () => {\n            durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n            durationSubscriber = null;\n            if (hasValue) {\n                hasValue = false;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n            isComplete && subscriber.complete();\n        };\n        const cleanupDuration = () => {\n            durationSubscriber = null;\n            isComplete && subscriber.complete();\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            lastValue = value;\n            if (!durationSubscriber) {\n                innerFrom(durationSelector(value)).subscribe((durationSubscriber = createOperatorSubscriber(subscriber, endDuration, cleanupDuration)));\n            }\n        }, () => {\n            isComplete = true;\n            (!hasValue || !durationSubscriber || durationSubscriber.closed) && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,KAAKA,CAACC,gBAAgB,EAAE;EACpC,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACtBF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACG,WAAW,CAAC,CAAC;MACxGH,kBAAkB,GAAG,IAAI;MACzB,IAAIF,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMM,KAAK,GAAGL,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACQ,IAAI,CAACD,KAAK,CAAC;MAC1B;MACAH,UAAU,IAAIJ,UAAU,CAACS,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC1BP,kBAAkB,GAAG,IAAI;MACzBC,UAAU,IAAIJ,UAAU,CAACS,QAAQ,CAAC,CAAC;IACvC,CAAC;IACDV,MAAM,CAACY,SAAS,CAACf,wBAAwB,CAACI,UAAU,EAAGO,KAAK,IAAK;MAC7DN,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGK,KAAK;MACjB,IAAI,CAACJ,kBAAkB,EAAE;QACrBR,SAAS,CAACG,gBAAgB,CAACS,KAAK,CAAC,CAAC,CAACI,SAAS,CAAER,kBAAkB,GAAGP,wBAAwB,CAACI,UAAU,EAAEK,WAAW,EAAEK,eAAe,CAAE,CAAC;MAC3I;IACJ,CAAC,EAAE,MAAM;MACLN,UAAU,GAAG,IAAI;MACjB,CAAC,CAACH,QAAQ,IAAI,CAACE,kBAAkB,IAAIA,kBAAkB,CAACS,MAAM,KAAKZ,UAAU,CAACS,QAAQ,CAAC,CAAC;IAC5F,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}