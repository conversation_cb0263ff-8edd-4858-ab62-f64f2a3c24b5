<nav class="sidebar d-flex flex-column">
  <div class="sidebar-header p-4 text-center border-bottom border-light">
    <h5 class="text-white mb-0">
      <i class="fas fa-store me-2"></i>
      EIM System
    </h5>
    <small class="text-light opacity-75">Inventory Management</small>
  </div>

  <div class="sidebar-menu flex-grow-1 p-3">
    <ul class="nav flex-column">
      <li class="nav-item" *ngFor="let item of menuItems">
        <a *ngIf="hasAccess(item.roles)"
           class="nav-link d-flex align-items-center"
           [routerLink]="item.route"
           [class.active]="isActiveRoute(item.route)">
          <i [class]="item.icon + ' me-3'"></i>
          {{ item.label }}
        </a>
      </li>
    </ul>
  </div>

  <div class="sidebar-footer p-3 border-top border-light" *ngIf="currentUser">
    <div class="d-flex align-items-center text-white">
      <div class="avatar bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center me-3" 
           style="width: 40px; height: 40px;">
        <i class="fas fa-user"></i>
      </div>
      <div class="flex-grow-1">
        <div class="fw-bold">{{ currentUser.first_name }}</div>
        <small class="opacity-75">{{ currentUser.role | titlecase }}</small>
      </div>
    </div>
  </div>
</nav>
