<header class="header bg-white shadow-sm border-bottom">
  <div class="container-fluid">
    <div class="row align-items-center py-3">
      <div class="col-md-6">
        <h4 class="mb-0 text-dark">
          <i class="fas fa-boxes me-2 text-primary"></i>
          Inventory Management
        </h4>
      </div>
      <div class="col-md-6">
        <div class="d-flex align-items-center justify-content-end">
          <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" 
                    type="button" 
                    id="userDropdown" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
              <i class="fas fa-user-circle me-2"></i>
              <span *ngIf="currentUser">
                {{ currentUser.first_name }} {{ currentUser.last_name }}
              </span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
              <li>
                <span class="dropdown-item-text">
                  <small class="text-muted">Role: {{ currentUser?.role | titlecase }}</small>
                </span>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="#" (click)="logout()">
                  <i class="fas fa-sign-out-alt me-2"></i>
                  Logout
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
